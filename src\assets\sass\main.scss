@import 'libs/vars';
@import 'libs/functions';
@import 'libs/mixins';
@import 'libs/vendor';
@import 'libs/breakpoints';
@import 'libs/html-grid';
@import url('fontawesome-all.min.css');
@import url("https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400italic");

/*
	Strata by HTML5 UP
	html5up.net | @ajlkn
	Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
*/

$size-header-width: 35%;
$size-header-pad: 4em;

// Breakpoints.

	@include breakpoints((
		xlarge:  ( 1281px,  1800px ),
		large:   ( 981px,   1280px ),
		medium:  ( 737px,   980px  ),
		small:   ( 481px,   736px  ),
		xsmall:  ( null,    480px  ),
	));

// Reset.
// Based on meyerweb.com/eric/tools/css/reset (v2.0 | 20110126 | License: public domain)

	html, body, div, span, applet, object,
	iframe, h1, h2, h3, h4, h5, h6, p, blockquote,
	pre, a, abbr, acronym, address, big, cite,
	code, del, dfn, em, img, ins, kbd, q, s, samp,
	small, strike, strong, sub, sup, tt, var, b,
	u, i, center, dl, dt, dd, ol, ul, li, fieldset,
	form, label, legend, table, caption, tbody,
	tfoot, thead, tr, th, td, article, aside,
	canvas, details, embed, figure, figcaption,
	footer, header, hgroup, menu, nav, output, ruby,
	section, summary, time, mark, audio, video {
		margin: 0;
		padding: 0;
		border: 0;
		font-size: 100%;
		font: inherit;
		vertical-align: baseline;
	}

	article, aside, details, figcaption, figure,
	footer, header, hgroup, menu, nav, section {
		display: block;
	}

	body {
		line-height: 1;
	}

	ol, ul {
		list-style:none;
	}

	blockquote,	q {
		quotes: none;

		&:before,
		&:after {
			content: '';
			content: none;
		}
	}

	table {
		border-collapse: collapse;
		border-spacing: 0;
	}

	body {
		-webkit-text-size-adjust: none;
	}

	mark {
		background-color: transparent;
		color: inherit;
	}

	input::-moz-focus-inner {
		border: 0;
		padding: 0;
	}

	input, select, textarea {
		-moz-appearance: none;
		-webkit-appearance: none;
		-ms-appearance: none;
		appearance: none;
	}

/* Basic */

	// Set box model to border-box.
	// Based on css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice
		html {
			box-sizing: border-box;
		}

		*, *:before, *:after {
			box-sizing: inherit;
		}

	body {
		background: _palette(bg);

		// Stops initial animations until page loads.
			&.is-preload {
				*, *:before, *:after {
					@include vendor('animation', 'none !important');
					@include vendor('transition', 'none !important');
				}
			}

	}

	body, input, select, textarea {
		color: _palette(fg);
		font-family: _font(family);
		font-size: 16pt;
		font-weight: _font(weight);
		line-height: 1.75em;
	}

	a {
		@include vendor('transition', ('color #{_duration(transition)} ease-in-out', 'border-color #{_duration(transition)} ease-in-out'));
		border-bottom: dotted 1px;
		color: _palette(accent1, bg);
		text-decoration: none;

		&:hover {
			border-bottom-color: transparent;
			color: _palette(accent1, bg) !important;
			text-decoration: none;
		}
	}

	strong, b {
		color: _palette(fg-bold);
		font-weight: _font(weight-bold);
	}

	em, i {
		font-style: italic;
	}

	p {
		margin: 0 0 _size(element-margin) 0;
	}

	h1, h2, h3, h4, h5, h6 {
		color: _palette(fg-bold);
		font-weight: _font(weight-bold);
		line-height: 1em;
		margin: 0 0 (_size(element-margin) * 0.5) 0;

		a {
			color: inherit;
			text-decoration: none;
		}
	}

	h1 {
		font-size: 2em;
		line-height: 1.5em;
	}

	h2 {
		font-size: 1.5em;
		line-height: 1.5em;
	}

	h3 {
		font-size: 1.25em;
		line-height: 1.5em;
	}

	h4 {
		font-size: 1.1em;
		line-height: 1.5em;
	}

	h5 {
		font-size: 0.9em;
		line-height: 1.5em;
	}

	h6 {
		font-size: 0.7em;
		line-height: 1.5em;
	}

	sub {
		font-size: 0.8em;
		position: relative;
		top: 0.5em;
	}

	sup {
		font-size: 0.8em;
		position: relative;
		top: -0.5em;
	}

	hr {
		border: 0;
		border-bottom: solid 2px _palette(border);

		// This is the *only* instance where we need to rely on margin collapse.
		margin: _size(element-margin) 0;

		&.major {
			margin: (_size(element-margin) * 1.5) 0;
		}
	}

	blockquote {
		border-left: solid 6px _palette(border);
		font-style: italic;
		margin: 0 0 _size(element-margin) 0;
		padding: 0.5em 0 0.5em 1.5em;
	}

	code {
		background: _palette(border-bg);
		border-radius: _size(border-radius);
		border: solid 2px _palette(border);
		font-family: _font(family-fixed);
		font-size: 0.9em;
		margin: 0 0.25em;
		padding: 0.25em 0.65em;
	}

	pre {
		-webkit-overflow-scrolling: touch;
		font-family: _font(family-fixed);
		font-size: 0.9em;
		margin: 0 0 _size(element-margin) 0;

		code {
			display: block;
			line-height: 1.75em;
			padding: 1em 1.5em;
			overflow-x: auto;
		}
	}

	.align-left {
		text-align: left;
	}

	.align-center {
		text-align: center;
	}

	.align-right {
		text-align: right;
	}

/* Container */

	.container {
		margin: 0 auto;
		max-width: calc(100% - #{_size(element-margin) * 2});
		width: _size(container-width);

		&.xsmall {
			width: (_size(container-width) * 0.25);
		}

		&.small {
			width: (_size(container-width) * 0.5);
		}

		&.medium {
			width: (_size(container-width) * 0.75);
		}

		&.large {
			width: (_size(container-width) * 1.25);
		}

		&.xlarge {
			width: (_size(container-width) * 1.5);
		}

		&.max {
			width: 100%;
		}

		@include breakpoint('<=medium') {
			width: 100% !important;
			max-width: 100% !important;
		}

		@include breakpoint('<=xsmall') {
			max-width: calc(100% - #{_size(element-margin) * 1.5});
		}
	}

/* Row */

	.row {
		@include html-grid(2.5em);

		@include breakpoint('<=xlarge') {
			@include html-grid(2.5em, 'xlarge');
		}

		@include breakpoint('<=large') {
			@include html-grid(2em, 'large');
		}

		@include breakpoint('<=medium') {
			@include html-grid(2em, 'medium');
		}

		@include breakpoint('<=small') {
			@include html-grid(1.5em, 'small');
		}

		@include breakpoint('<=xsmall') {
			@include html-grid(1.5em, 'xsmall');
		}
	}

/* Section/Article */

	section, article {
		&.special {
			text-align: center;
		}
	}

	header {
		p {
			color: _palette(fg-light);
			position: relative;
			margin: 0 0 (_size(element-margin) * 0.75) 0;
		}

		h2 + p {
			font-size: 1.25em;
			margin-top: (_size(element-margin) * -0.5);
			line-height: 1.5em;
		}

		h3 + p {
			font-size: 1.1em;
			margin-top: (_size(element-margin) * -0.4);
			line-height: 1.5em;
		}

		h4 + p,
		h5 + p,
		h6 + p {
			font-size: 0.9em;
			margin-top: (_size(element-margin) * -0.3);
			line-height: 1.5em;
		}

		&.major {
			h2 {
				font-size: 2em;
			}
		}
	}

/* Form */

	form {
		margin: 0 0 _size(element-margin) 0;
	}

	label {
		color: _palette(fg-bold);
		display: block;
		font-size: 0.9em;
		font-weight: _font(weight-bold);
		margin: 0 0 (_size(element-margin) * 0.5) 0;
	}

	input[type="text"],
	input[type="password"],
	input[type="email"],
	select,
	textarea {
		@include vendor('appearance', 'none');
		background: _palette(border-bg);
		border-radius: _size(border-radius);
		border: solid 2px transparent;
		color: inherit;
		display: block;
		outline: 0;
		padding: 0 0.75em;
		text-decoration: none;
		width: 100%;

		&:invalid {
			box-shadow: none;
		}

		&:focus {
			border-color: _palette(accent1, bg);
		}
	}

	select {
		background-image: svg-url("<svg xmlns='http://www.w3.org/2000/svg' width='40' height='40' preserveAspectRatio='none' viewBox='0 0 40 40'><path d='M9.4,12.3l10.4,10.4l10.4-10.4c0.2-0.2,0.5-0.4,0.9-0.4c0.3,0,0.6,0.1,0.9,0.4l3.3,3.3c0.2,0.2,0.4,0.5,0.4,0.9 c0,0.4-0.1,0.6-0.4,0.9L20.7,31.9c-0.2,0.2-0.5,0.4-0.9,0.4c-0.3,0-0.6-0.1-0.9-0.4L4.3,17.3c-0.2-0.2-0.4-0.5-0.4-0.9 c0-0.4,0.1-0.6,0.4-0.9l3.3-3.3c0.2-0.2,0.5-0.4,0.9-0.4S9.1,12.1,9.4,12.3z' fill='#{_palette(border2)}' /></svg>");
		background-size: 1.25rem;
		background-repeat: no-repeat;
		background-position: calc(100% - 1rem) center;
		height: _size(element-height);
		padding-right: _size(element-height);
		text-overflow: ellipsis;

		option {
			color: _palette(fg-bold);
			background: _palette(bg);
		}

		&:focus {
			&::-ms-value {
				background-color: transparent;
			}
		}

		&::-ms-expand {
			display: none;
		}
	}

	input[type="text"],
	input[type="password"],
	input[type="email"],
	select {
		height: _size(element-height);
	}

	textarea {
		padding: 0.75em;
	}

	input[type="checkbox"],
	input[type="radio"] {
		@include vendor('appearance', 'none');
		display: block;
		float: left;
		margin-right: -2em;
		opacity: 0;
		width: 1em;
		z-index: -1;

		& + label {
			@include icon(false, solid);
			color: _palette(fg);
			cursor: pointer;
			display: inline-block;
			font-size: 1em;
			font-weight: _font(weight);
			padding-left: (_size(element-height) * 0.6) + 0.75em;
			padding-right: 0.75em;
			position: relative;

			&:before {
				background: _palette(border-bg);
				border-radius: _size(border-radius);
				border: solid 2px transparent;
				content: '';
				display: inline-block;
				font-size: 0.8em;
				height: (_size(element-height) * 0.75);
				left: 0;
				line-height: (_size(element-height) * 0.675);
				position: absolute;
				text-align: center;
				top: 0;
				width: (_size(element-height) * 0.75);
			}
		}

		&:checked + label {
			&:before {
				background: _palette(fg-bold);
				border-color: _palette(fg-bold);
				color: _palette(bg);
				content: '\f00c';
			}
		}

		&:focus + label {
			&:before {
				border-color: _palette(accent1, bg);
			}
		}
	}

	input[type="checkbox"] {
		& + label {
			&:before {
				border-radius: _size(border-radius);
			}
		}
	}

	input[type="radio"] {
		& + label {
			&:before {
				border-radius: 100%;
			}
		}
	}

	::-webkit-input-placeholder {
		color: _palette(fg-light) !important;
		opacity: 1.0;
	}

	:-moz-placeholder {
		color: _palette(fg-light) !important;
		opacity: 1.0;
	}

	::-moz-placeholder {
		color: _palette(fg-light) !important;
		opacity: 1.0;
	}

	:-ms-input-placeholder {
		color: _palette(fg-light) !important;
		opacity: 1.0;
	}

/* Box */

	.box {
		border-radius: _size(border-radius);
		border: solid 2px _palette(border);
		margin-bottom: _size(element-margin);
		padding: 1.5em;

		> :last-child,
		> :last-child > :last-child,
		> :last-child > :last-child > :last-child {
			margin-bottom: 0;
		}

		&.alt {
			border: 0;
			border-radius: 0;
			padding: 0;
		}
	}

/* Icon */

	.icon {
		@include icon;
		border-bottom: none;
		position: relative;

		> .label {
			display: none;
		}

		&:before {
			line-height: inherit;
		}

		&.solid {
			&:before {
				font-weight: 900;
			}
		}

		&.brands {
			&:before {
				font-family: 'Font Awesome 5 Brands';
			}
		}
	}

/* Image */

	.image {
		border-radius: _size(border-radius);
		border: 0;
		display: inline-block;
		position: relative;

		&:before {
			@include vendor('transition', 'opacity #{_duration(transition)} ease-in-out');
			background: url('images/overlay.png');
			border-radius: _size(border-radius);
			content: '';
			display: block;
			height: 100%;
			left: 0;
			opacity: 0.5;
			position: absolute;
			top: 0;
			width: 100%;
		}

		&.thumb {
			text-align: center;

			&:after {
				@include vendor('transition', 'opacity #{_duration(transition)} ease-in-out');
				border-radius: _size(border-radius);
				border: solid 3px rgba(255,255,255,0.5);
				color: #fff;
				content: 'View';
				display: inline-block;
				font-size: 0.8em;
				font-weight: _font(weight-bold);
				left: 50%;
				line-height: 2.25em;
				margin: -1.25em 0 0 -3em;
				opacity: 0;
				padding: 0 1.5em;
				position: absolute;
				text-align: center;
				text-decoration: none;
				top: 50%;
				white-space: nowrap;
			}

			&:hover {
				&:after {
					opacity: 1.0;
				}

				&:before {
					background: url('images/overlay.png'), url('images/overlay.png');
					opacity: 1.0;
				}
			}
		}

		img {
			border-radius: _size(border-radius);
			display: block;
		}

		&.left {
			float: left;
			margin: 0 1.5em 1em 0;
			top: 0.25em;
		}

		&.right {
			float: right;
			margin: 0 0 1em 1.5em;
			top: 0.25em;
		}

		&.left,
		&.right {
			max-width: 40%;

			img {
				width: 100%;
			}
		}

		&.fit {
			display: block;
			margin: 0 0 _size(element-margin) 0;
			width: 100%;

			img {
				width: 100%;
			}
		}

		&.avatar {
			border-radius: 100%;

			&:before {
				display: none;
			}

			img {
				border-radius: 100%;
				width: 100%;
			}
		}
	}

/* List */

	ol {
		list-style: decimal;
		margin: 0 0 _size(element-margin) 0;
		padding-left: 1.25em;

		li {
			padding-left: 0.25em;
		}
	}

	ul {
		list-style: disc;
		margin: 0 0 _size(element-margin) 0;
		padding-left: 1em;

		li {
			padding-left: 0.5em;
		}

		&.alt {
			list-style: none;
			padding-left: 0;

			li {
				border-top: solid 2px _palette(border);
				padding: 0.5em 0;

				&:first-child {
					border-top: 0;
					padding-top: 0;
				}
			}
		}
	}

	dl {
		margin: 0 0 _size(element-margin) 0;
	}

/* Icons */

	ul.icons {
		cursor: default;
		list-style: none;
		padding-left: 0;

		li {
			display: inline-block;
			padding: 0 1em 0 0;

			&:last-child {
				padding-right: 0;
			}

			.icon {
				&:before {
					font-size: 1.5em;
				}
			}
		}
	}

/* Labeled Icons */

	ul.labeled-icons {
		list-style: none;
		padding: 0;

		li {
			line-height: 1.75em;
			margin: 1.5em 0 0 0;
			padding-left: 2.25em;
			position: relative;

			&:first-child {
				margin-top: 0;
			}

			a {
				color: inherit;
			}

			h3 {
				color: _palette(fg-light);
				left: 0;
				position: absolute;
				text-align: center;
				top: 0;
				width: 1em;
			}
		}
	}

/* Actions */

	ul.actions {
		@include vendor('display', 'flex');
		cursor: default;
		list-style: none;
		margin-left: (_size(element-margin) * -0.5);
		padding-left: 0;

		li {
			padding: 0 0 0 (_size(element-margin) * 0.5);
			vertical-align: middle;
		}

		&.special {
			@include vendor('justify-content', 'center');
			width: 100%;
			margin-left: 0;

			li {
				&:first-child {
					padding-left: 0;
				}
			}
		}

		&.stacked {
			@include vendor('flex-direction', 'column');
			margin-left: 0;

			li {
				padding: (_size(element-margin) * 0.65) 0 0 0;

				&:first-child {
					padding-top: 0;
				}
			}
		}

		&.fit {
			width: calc(100% + #{_size(element-margin) * 0.5});

			li {
				@include vendor('flex-grow', '1');
				@include vendor('flex-shrink', '1');
				width: 100%;

				> * {
					width: 100%;
				}
			}

			&.stacked {
				width: 100%;
			}
		}

		@include breakpoint('<=xsmall') {
			&:not(.fixed) {
				@include vendor('flex-direction', 'column');
				margin-left: 0;
				width: 100% !important;

				li {
					@include vendor('flex-grow', '1');
					@include vendor('flex-shrink', '1');
					padding: (_size(element-margin) * 0.5) 0 0 0;
					text-align: center;
					width: 100%;

					> * {
						width: 100%;
					}

					&:first-child {
						padding-top: 0;
					}

					input[type="submit"],
					input[type="reset"],
					input[type="button"],
					button,
					.button {
						width: 100%;

						&.icon {
							&:before {
								margin-left: -0.5em;
							}
						}
					}
				}
			}
		}
	}

/* Table */

	.table-wrapper {
		-webkit-overflow-scrolling: touch;
		overflow-x: auto;
	}

	table {
		margin: 0 0 _size(element-margin) 0;
		width: 100%;

		tbody {
			tr {
				border: solid 1px _palette(border);
				border-left: 0;
				border-right: 0;

				&:nth-child(2n + 1) {
					background-color: _palette(border-bg);
				}
			}
		}

		td {
			padding: 0.75em 0.75em;
		}

		th {
			color: _palette(fg-bold);
			font-size: 0.9em;
			font-weight: _font(weight-bold);
			padding: 0 0.75em 0.75em 0.75em;
			text-align: left;
		}

		thead {
			border-bottom: solid 2px _palette(border);
		}

		tfoot {
			border-top: solid 2px _palette(border);
		}

		&.alt {
			border-collapse: separate;

			tbody {
				tr {
					td {
						border: solid 2px _palette(border);
						border-left-width: 0;
						border-top-width: 0;

						&:first-child {
							border-left-width: 2px;
						}
					}

					&:first-child {
						td {
							border-top-width: 2px;
						}
					}
				}
			}

			thead {
				border-bottom: 0;
			}

			tfoot {
				border-top: 0;
			}
		}
	}

/* Button */

	input[type="submit"],
	input[type="reset"],
	input[type="button"],
	.button {
		@include vendor('appearance', 'none');
		@include vendor('transition', ('background-color #{_duration(transition)} ease-in-out', 'color #{_duration(transition)} ease-in-out', 'border-color #{_duration(transition)} ease-in-out'));
		background-color: transparent;
		border-radius: _size(border-radius);
		border: solid 3px _palette(border);
		color: _palette(fg-bold) !important;
		cursor: pointer;
		display: inline-block;
		font-weight: _font(weight-bold);
		height: 3.15em;
		height: calc(2.75em + 6px);
		line-height: 2.75em;
		min-width: 10em;
		padding: 0 1.5em;
		text-align: center;
		text-decoration: none;
		white-space: nowrap;

		&:hover {
			border-color: _palette(accent1, bg);
			color: _palette(accent1, bg) !important;
		}

		&:active {
			background-color: transparentize(_palette(accent1, bg), 0.9);
			border-color: _palette(accent1, bg);
			color: _palette(accent1, bg) !important;
		}

		&.icon {
			padding-left: 1.35em;

			&:before {
				margin-right: 0.5em;
			}
		}

		&.fit {
			min-width: 0;
			width: 100%;
		}

		&.small {
			font-size: 0.8em;
		}

		&.large {
			font-size: 1.35em;
		}

		&.primary {
			background-color: _palette(accent1, bg);
			border-color: _palette(accent1, bg);
			color: _palette(accent1, fg-bold) !important;

			&:hover {
				background-color: lighten(_palette(accent1, bg), 5);
				border-color: lighten(_palette(accent1, bg), 5);
			}

			&:active {
				background-color: darken(_palette(accent1, bg), 5);
				border-color: darken(_palette(accent1, bg), 5);
			}
		}

		&.disabled,
		&:disabled {
			background-color: _palette(border2-bg) !important;
			border-color: _palette(border2-bg) !important;
			color: _palette(fg-light) !important;
			cursor: default;
		}
	}

/* Work Item */

	.work-item {
		margin: 0 0 _size(element-margin) 0;

		.image {
			margin: 0 0 (_size(element-margin) * 0.75) 0;
		}

		h3 {
			font-size: 1em;
			margin: 0 0 (_size(element-margin) * 0.25) 0;
		}

		p {
			font-size: 0.8em;
			line-height: 1.5em;
			margin: 0;
		}
	}

/* Header */

	#header {
		@include vendor('display', 'flex');
		@include vendor('flex-direction', 'column');
		@include vendor('align-items', 'flex-end');
		@include vendor('justify-content', 'space-between');
		background-color:		_palette(accent2, bg);
		background-attachment:	scroll,								scroll;
		background-image:		url('images/overlay.png'),			url('../../images/bg.jpg');
		background-position:	top left,							top left;
		background-repeat:		repeat,								no-repeat;
		background-size:		auto,								150%;
		color: _palette(accent2, fg);
		height: 100%;
		left: 0;
		padding: ($size-header-pad * 2) $size-header-pad;
		position: fixed;
		text-align: right;
		top: 0;
		width: $size-header-width;

		> * {
			@include vendor('flex-shrink', '0');
			width: 100%;
		}

		> .inner {
			@include vendor('flex-grow', '1');
			margin: 0 0 ($size-header-pad * 0.5) 0;
		}

		strong, b {
			color: _palette(accent2, fg-bold);
		}

		h2, h3, h4, h5, h6 {
			color: _palette(accent2, fg-bold);
		}

		h1 {
			color: _palette(accent2, fg);
			font-size: 1.35em;
			line-height: 1.75em;
			margin: 0;
		}

		.image.avatar {
			margin: 0 0 (_size(element-margin) * 0.5) 0;
			width: 6.25em;
		}
	}

/* Footer */

	#footer {
		.icons {
			margin: (_size(element-margin) * 0.5) 0 0 0;

			a {
				color: _palette(accent2, fg-light);
			}
		}

		.copyright {
			color: _palette(accent2, fg-light);
			font-size: 0.8em;
			list-style: none;
			margin: (_size(element-margin) * 0.5) 0 0 0;
			padding: 0;

			li {
				border-left: solid 1px _palette(accent2, border);
				display: inline-block;
				line-height: 1em;
				margin-left: 0.75em;
				padding-left: 0.75em;

				&:first-child {
					border-left: 0;
					margin-left: 0;
					padding-left: 0;
				}

				a {
					color: inherit;
				}
			}
		}
	}

/* Main */

	#main {
		margin-left: $size-header-width;
		max-width: 50em + $size-header-pad;
		padding: ($size-header-pad * 2) $size-header-pad $size-header-pad $size-header-pad;
		width: calc(100% - #{$size-header-width});

		> section {
			border-top: solid 2px _palette(border);
			margin: $size-header-pad 0 0 0;
			padding: $size-header-pad 0 0 0;

			&:first-child {
				border-top: 0;
				margin-top: 0;
				padding-top: 0;
			}
		}
	}

/* Poptrox */

	@include keyframes('spin') {
		0% { @include vendor('transform', 'rotate(0deg)'); }
		100% { @include vendor('transform', 'rotate(360deg)'); }
	};

	.poptrox-popup {
		@include vendor('box-sizing', 'content-box');
		-webkit-tap-highlight-color: rgba(255,255,255,0);
		background: #fff;
		border-radius: _size(border-radius);
		box-shadow: 0 0.1em 0.15em 0 rgba(0,0,0,0.15);
		overflow: hidden;
		padding-bottom: 3em;

		.loader {
			@include icon(false, solid);
			@include vendor('animation', 'spin 1s linear infinite');
			font-size: 1.5em;
			height: 1em;
			left: 50%;
			line-height: 1em;
			margin: -0.5em 0 0 -0.5em;
			position: absolute;
			top: 50%;
			width: 1em;

			&:before {
				content: '\f1ce';
			}
		}

		.caption {
			background: #fff;
			bottom: 0;
			cursor: default;
			font-size: 0.9em;
			height: 3em;
			left: 0;
			line-height: 2.8em;
			position: absolute;
			text-align: center;
			width: 100%;
			z-index: 1;
		}

		.nav-next,
		.nav-previous {
			@include icon(false, solid);
			@include vendor('transition', 'opacity #{_duration(transition)} ease-in-out');
			-webkit-tap-highlight-color: rgba(255,255,255,0);
			background: rgba(0,0,0,0.01);
			cursor: pointer;
			height: 100%;
			opacity: 0;
			position: absolute;
			top: 0;
			width: 50%;

			&:before {
				color: #fff;
				font-size: 2.5em;
				height: 1em;
				line-height: 1em;
				margin-top: -0.75em;
				position: absolute;
				text-align: center;
				top: 50%;
				width: 1.5em;
			}
		}

		.nav-next {
			right: 0;

			&:before {
				content: '\f105';
				right: 0;
			}
		}

		.nav-previous {
			left: 0;

			&:before {
				content: '\f104';
				left: 0;
			}
		}

		.closer {
			@include icon(false, solid);
			@include vendor('transition', 'opacity #{_duration(transition)} ease-in-out');
			-webkit-tap-highlight-color: rgba(255,255,255,0);
			color: #fff;
			height: 4em;
			line-height: 4em;
			opacity: 0;
			position: absolute;
			right: 0;
			text-align: center;
			top: 0;
			width: 4em;
			z-index: 2;

			&:before {
				@include vendor('box-sizing', 'content-box');
				border-radius: 100%;
				border: solid 3px rgba(255,255,255,0.5);
				content: '\f00d';
				display: block;
				font-size: 1em;
				height: 1.75em;
				left: 50%;
				line-height: 1.75em;
				margin: -0.875em 0 0 -0.875em;
				position: absolute;
				top: 50%;
				width: 1.75em;
			}
		}

		&:hover {
			.nav-next,
			.nav-previous {
				opacity: 0.5;

				&:hover {
					opacity: 1.0;
				}
			}

			.closer {
				opacity: 0.5;

				&:hover {
					opacity: 1.0;
				}
			}
		}
	}

/* Touch */

	body.is-touch {

		.image {
			&.thumb {
				&:before {
					opacity: 0.5 !important;
				}

				&:after {
					display: none !important;
				}
			}
		}

		#header {
			background-attachment:	scroll;
			background-size:		auto, cover;
		}

		.poptrox-popup {
			.nav-next,
			.nav-previous,
			.closer {
				opacity: 1.0 !important;
			}
		}

	}

/* XLarge */

	@include breakpoint('<=xlarge') {

		/* Basic */

			body, input, select, textarea {
				font-size: 12pt;
			}

	}

/* Large */

	@include breakpoint('<=large') {

		$size-header-width: 30%;
		$size-header-pad: 3em;

		/* Header */

			#header {
				padding: ($size-header-pad * 2) $size-header-pad $size-header-pad $size-header-pad;
				width: $size-header-width;

				h1 {
					font-size: 1.25em;

					br {
						display: none;
					}
				}

				> .inner {
					margin-bottom: 0;
				}
			}

		/* Footer */

			#footer {
				.copyright {
					li {
						border-left-width: 0;
						display: block;
						line-height: 2.25em;
						margin-left: 0;
						padding-left: 0;
					}
				}
			}

		/* Main */

			#main {
				margin-left: $size-header-width;
				max-width: none;
				padding: ($size-header-pad * 2) $size-header-pad $size-header-pad $size-header-pad;
				width: calc(100% - #{$size-header-width});
			}

	}

/* Medium */

	@include breakpoint('<=medium') {

		$size-header-pad: 4em;

		/* Basic */

			h1, h2, h3, h4, h5, h6 {
				br {
					display: none;
				}
			}

		/* List */

			ul {
				&.icons {
					li {
						.icon {
							font-size: 1.25em;
						}
					}
				}
			}

		/* Header */

			#header {
				background-attachment:	scroll;
				background-position:	top left,	center center;
				background-size:		auto,		cover;
				left: auto;
				padding: ($size-header-pad * 1.5) $size-header-pad;
				position: relative;
				text-align: center;
				top: auto;
				width: 100%;
				display: block;

				h1 {
					font-size: 1.75em;

					br {
						display: inline;
					}
				}
			}

		/* Footer */

			#footer {
				background-attachment:	scroll;
				background-color:		_palette(accent2, bg);
				background-image:		url('images/overlay.png'),		url('../../images/bg.jpg');
				background-position:	top left,						bottom center;
				background-repeat:		repeat,							no-repeat;
				background-size:		auto,							cover;
				bottom: auto;
				left: auto;
				padding: $size-header-pad $size-header-pad ($size-header-pad * 1.5) $size-header-pad;
				position: relative;
				text-align: center;
				width: 100%;

				.icons {
					margin: 0 0 (_size(element-margin) * 0.5) 0;
				}

				.copyright {
					margin: 0 0 (_size(element-margin) * 0.5) 0;

					li {
						border-left-width: 1px;
						display: inline-block;
						line-height: 1em;
						margin-left: 0.75em;
						padding-left: 0.75em;
					}
				}
			}

		/* Main */

			#main {
				margin: 0;
				padding: ($size-header-pad * 1.5) $size-header-pad;
				width: 100%;
			}

	}

/* Small */

	@include breakpoint('<=small') {

		$size-header-pad: 1.5em;

		/* Basic */

			h1 {
				font-size: 1.5em;
			}

			h2 {
				font-size: 1.2em;
			}

			h3 {
				font-size: 1em;
			}

		/* Section/Article */

			section, article {
				&.special {
					text-align: center;
				}
			}

			header {
				&.major {
					h2 {
						font-size: 1.35em;
					}
				}
			}

		/* List */

			ul {
				&.labeled-icons {
					li {
						padding-left: 2em;

						h3 {
							line-height: 1.75em;
						}
					}
				}
			}

		/* Header */

			#header {
				padding: ($size-header-pad * 1.5) $size-header-pad;

				h1 {
					font-size: 1.35em;
				}
			}

		/* Footer */

			#footer {
				padding: ($size-header-pad * 1.5) $size-header-pad;
			}

		/* Main */

			#main {
				padding: ($size-header-pad * 1.5) $size-header-pad (($size-header-pad * 1.5) - _size(element-margin)) $size-header-pad;

				> section {
					margin: ($size-header-pad * 1.5) 0 0 0;
					padding: ($size-header-pad * 1.5) 0 0 0;
				}
			}

		/* Poptrox */

			.poptrox-popup {
				border-radius: 0;

				.nav-next,
				.nav-previous {
					&:before {
						margin-top: -1em;
					}
				}
			}

	}

/* XSmall */

	@include breakpoint('<=xsmall') {

		$size-header-pad: 1.5em;

		/* Header */

			#header {
				padding: ($size-header-pad * 3) $size-header-pad;

				h1 {
					br {
						display: none;
					}
				}
			}

		/* Footer */

			#footer {
				.copyright {
					li {
						border-left-width: 0;
						display: block;
						line-height: 2.25em;
						margin-left: 0;
						padding-left: 0;
					}
				}
			}

	}