import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { AppConfig } from 'src/app/configs/app.config';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { PrivacyPolicy } from 'src/app/shared/models/privacy_policy_model';

@Injectable({
  providedIn: 'root'
})
export class PrivacyService {

  webAPIEndPoint:string='';
  constructor(private httpClient: HttpClient) {
    this.webAPIEndPoint = AppConfig.CabYaari_WebAPI;
  }


  getPrivacyPolicy(): Observable<PrivacyPolicy> {
    let apiURL = this.webAPIEndPoint+'/api/Home/cabyaari/privacypolicy';
    return this.httpClient.get<PrivacyPolicy>(
      apiURL
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    )
  }
}
