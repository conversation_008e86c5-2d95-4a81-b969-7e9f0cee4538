import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { map } from 'rxjs/operators';
import { LoggerService } from '../../interceptors/logger.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(
    private router: Router, 
    private authService: AuthService) {
    this.logger = LoggerService.createLogger('AuthGuard');
  }

  private logger: LoggerService;

  canActivate(next: ActivatedRouteSnapshot):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    return this.authService.user$.pipe(
      map((user) => {
        this.logger.trace('user', user);

        const urlSegments = next.url;
        this.logger.trace('urlSegments', urlSegments);

        // Build the complete URL path from all segments
        const urlPath = '/' + urlSegments.map(segment => segment.path).join('/');
        this.logger.trace('urlPath', urlPath);

        const queryParams = next.queryParams;
        this.logger.trace('queryParams', queryParams);

        if (user) {
          return true;
        } else {
          // Build the complete return URL with query parameters
          const queryString = Object.keys(queryParams).length > 0
            ? '?' + Object.keys(queryParams).map(key => `${key}=${encodeURIComponent(queryParams[key])}`).join('&')
            : '';
          const fullReturnUrl = urlPath + queryString;

          this.logger.trace('fullReturnUrl', fullReturnUrl);

          this.router.navigate(
            ['login'],
            { queryParams: { returnUrl: fullReturnUrl } }
          );
          return false;
        }
      })
    );
  }
}
