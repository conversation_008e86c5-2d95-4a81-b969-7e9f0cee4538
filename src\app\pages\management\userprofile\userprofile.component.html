<app-progress-loader></app-progress-loader>

<app-sidemenu></app-sidemenu>

<header id="header">
    <span class="toggle menu-toggle"> <img src="{{imageFolderPath}}/toggle-icon.png"> </span>
    <div class="topheader loginheader">
        <a routerLink="/home" class="logo">
            <img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;"/>
        </a>
    </div>

    <div class="profile-pan">
        
        <div class="pro-image">
            <span 
                class="pull-right hoverable" 
                (click)="fileSelect.click()"
                data-toggle="tooltip" 
                data-placement="left" 
                title="Edit Profile Photo">
                
                <i class="fa fa-edit"></i>
            
            </span>

            <input
                #fileSelect
                class="d-none"
                type="file" 
                accept="image/*"
                (change)="uploadProfilePhoto(fileSelect)"/>

            <div class="user-image">
                <img [src]="imageUrl">
            </div>
            
            <div class="name">
                {{ user?.firstName }} &nbsp; {{ user?.lastName }}
            </div>
        </div>
    </div>

    <div class="driver-info">
        <div class="info-box">
            <button (click)="togglePersonalInformation()" class="w3-button w3-block w3-black w3-left-align">
                Personal Information
                <span class="fa fa-sort-down"></span>
            </button>
            
            <div *ngIf="showPersonalInformation" id="Demo1" class="w3-border">
                <ul class="w3-ul">
                  <li><i class="fa fa-envelope"></i>{{ user?.email }}</li>
                  <li><i class="fa fa-mobile-alt"></i>{{user?.phoneNumber}}</li>
                  <li><i class="fa fa-home"></i> Rajsthan</li>
                </ul>
            </div>
        </div>

        <div>
            <div class="login" (click)="logout()">Log Out</div>
        </div>
    </div>
</header>

<router-outlet></router-outlet>

<!-- <app-user-rides></app-user-rides> -->