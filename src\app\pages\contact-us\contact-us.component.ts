import { Component, OnInit } from '@angular/core';
import { AppConfig } from 'src/app/configs/app.config';
import { ToastrService } from 'ngx-toastr';
import { ContactUs } from 'src/app/shared/models/contact-us.model';
import { ValidationMessages } from 'src/app/shared/constants/message.content';
import { CompanyGenericService } from 'src/app/shared/services/company-generic.service';
import { APIResponse } from 'src/app/shared/models/api-response';
import { RequestStatus } from 'src/app/shared/models/request-status';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-contact-us',
  templateUrl: './contact-us.component.html',
  styleUrls: ['./contact-us.component.css']
})
export class ContactUsComponent implements OnInit {

  contactUs: ContactUs = {
    name: '', emailId: '', phoneNumber: '', isWhatsAppNumber: true, enquiryMessage: ''
  };
  imageFolderPath: string = AppConfig.imageFolderPath;
  constructor(private toastr: ToastrService, private commonService: CompanyGenericService) { }

  ngOnInit(): void {
  }

   ContactUsRequest()
  {
    if(this.contactUs.name==='')
    {
      this.toastr.error(ValidationMessages.NAME,'Name Required !');
    }
    else if(this.contactUs.phoneNumber==='')
    {
      this.toastr.error(ValidationMessages.PHONE_NUMBER,'Last Name Required !');
    }
    else if(this.contactUs.emailId==='')
    {
      this.toastr.error(ValidationMessages.EMAIL_ID,'Email ID Required !');
    }
    else if(this.contactUs.enquiryMessage==='')
    {
      this.toastr.error(ValidationMessages.QUERY,'First Address Required !');
    }
    else {
      this.commonService.contactUs(this.contactUs).subscribe(

        (response: APIResponse) => {
          if (response.data.attributes.status.toLowerCase() === RequestStatus.Executed.toString().toLocaleLowerCase()) {
            this.toastr.success('Thanks for sharing details ,we will contact to you shortly !');
            this.contactUs={
              name: '', emailId: '', phoneNumber: '', isWhatsAppNumber: true, enquiryMessage: ''
            }
          }
          else {
            this.toastr.error('Please try again!');
          }
        }, (error: HttpErrorResponse) => {
          this.toastr.error('Please try again!');
        }

      );
    }

  }


}
