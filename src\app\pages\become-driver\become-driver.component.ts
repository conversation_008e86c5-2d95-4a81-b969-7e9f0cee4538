import { Component, OnInit } from '@angular/core';
import { AppConfig } from 'src/app/configs/app.config';

import { ToastrService } from 'ngx-toastr';
import { BecomeDriver } from 'src/app/shared/models/become-driver.model';
import { ValidationMessages } from 'src/app/shared/constants/message.content';
import { CompanyGenericService } from 'src/app/shared/services/company-generic.service';
import { APIResponse } from 'src/app/shared/models/api-response';
import { RequestStatus } from 'src/app/shared/models/request-status';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-become-driver',
  templateUrl: './become-driver.component.html',
  styleUrls: ['./become-driver.component.css']
})
export class BecomeDriverComponent implements OnInit {

  becomeDriver: BecomeDriver =
    {
      driverFirtsName: '', driverLastName: '', driverEmailId: '', driverPhoneNumber: '', driverAddress: '',
      isWhatsAppNumber: false
    };

  imageFolderPath: string = AppConfig.imageFolderPath;
  constructor(private toastr: ToastrService, private commonService: CompanyGenericService) { }

  ngOnInit(): void {
  }

  BecomeDriverRequest() {
    if (this.becomeDriver.driverFirtsName === '') {
      this.toastr.error(ValidationMessages.FISRT_NAME, 'First Name Required !');
    }
    else if (this.becomeDriver.driverLastName === '') {
      this.toastr.error(ValidationMessages.LAST_NAME, 'Last Name Required !');
    }
    else if (this.becomeDriver.driverEmailId === '') {
      this.toastr.error(ValidationMessages.EMAIL_ID, 'Email ID Required !');
    }
    else if (this.becomeDriver.driverPhoneNumber === '') {
      this.toastr.error(ValidationMessages.PHONE_NUMBER, 'Phone Number Required !');
    }
    else if (this.becomeDriver.driverAddress === '') {
      this.toastr.error(ValidationMessages.ADDRESS, 'First Address Required !');
    }
    else {
      this.commonService.becomeDriver(this.becomeDriver).subscribe(
        (response: APIResponse) => {
          if (response.data.attributes.status.toLowerCase() === RequestStatus.Executed.toString().toLocaleLowerCase()) {
            this.toastr.success('Thanks for sharing details ,we will contact to you shortly !');
            this.becomeDriver = {
              driverFirtsName: '', driverLastName: '', driverEmailId: '', driverPhoneNumber: '', driverAddress: '',
      isWhatsAppNumber: false
            }
          }
          else {
            this.toastr.error('Please try again!');
          }
        }, (error: HttpErrorResponse) => {
          this.toastr.error('Please try again!');
        }

      );
    }


  }

}
