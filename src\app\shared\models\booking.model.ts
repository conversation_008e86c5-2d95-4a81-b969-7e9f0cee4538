export class BookingRequest {
    pickUpCity: string;
    dropOffCity: string;
    tripType: string;
    carCategory: string;
    carFeatures: string;
    carCapacity: number;
    carImage: string
    duration: string;
    distance: number;
    basicFare: number;
    driverCharge: number;
    gst: number;
    fare: number;
    gstFare: number;
    couponCode: string;
    couponDiscount: number;
    pickUpAddress: string;
    dropOffAddress: string;
    pickUpDate: string;
    pickUpTime: string;
    travelerName: string;
    phoneNumber: string;
    mailId: string;
    paymentMode: number;
    bookingCreatedBy: string;
    razorpayPaymentId: string;
    razorpayOrderid: string;
    razorpaySignature: string;
    razorpayStatus: string;
    pickUpAddressLongLat: string;
    dropOffAddressLongLat: string;
    cashAmountToPayDriver: number;
    paymentOption: number;
    tollCharge: number;
    fixRateNote: string;
    perKMCharges: string;
}
