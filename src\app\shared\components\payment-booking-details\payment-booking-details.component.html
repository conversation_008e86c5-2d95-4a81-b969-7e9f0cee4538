<app-progress-loader></app-progress-loader>

<header id="header" class="smallheader">
    <span class="toggle">
        <a routerLink="/pickupdropoffdetails" class="fa fa-arrow-left"></a> 
    </span>
    <div class="topheader">
        <a routerLink="/home" class="logo">
            <img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;"/>
        </a>
    </div>

    <div class="forminner">
        <div class="head"><span></span>Payment Option</div>
        <div class="payment-option">
            <div class="payment-box">
                <label class="payment-label">
                    <div class="payment-check">
                        <label class="custom-checkbox">
                            <input #paypartialCb name="paypartialCb" type="checkbox" (click)="toggleCheckbox(paypartialCb, payTotalCb);">
                            <span class="checkmark"></span>
                        </label>
                    </div>
                    <div class="payment-rate">
                        <span class="token-amount">Pay ₹ <span>{{ originalTokenAmount }}</span></span>
                        <span class="driver-pay">Pay rest to Driver</span>
                    </div>
                </label>
            </div>

            <div class="payment-box">
                <label class="payment-label">
                    <div class="payment-check">
                        <label class="custom-checkbox">
                            <input #payTotalCb name="payTotalCb" type="checkbox" (click)="toggleCheckbox(payTotalCb,paypartialCb);" checked>
                            <span class="checkmark"></span>
                        </label>
                    </div>
                    <div class="payment-rate">
                        <span class="token-amount">Pay <span>100%</span></span>
                        <span class="driver-pay">Pay the full amount</span>
                    </div>
                </label>
            </div>
        </div>
    </div>

    <div class="forminner mt-4">
        <div class="fare-table">
            <div class="fare-heading">
                <span>Total Fare</span>
                <span class="t-right">₹ {{fixedAmount}}</span>
            </div>

            <div class="amount-payable">
                <span>Amount Payable</span>
                <span class="t-right">₹ {{amountTobePaid}}</span>
            </div>

            <div class="driver-payable">
                <span>Cash to pay Driver</span>
                <span class="t-right">₹ {{bookingRequest?.cashAmountToPayDriver || 0}}</span>
            </div>
        </div>

    </div>



    <div class="forminner mt-4">
        <div>
            <div class="col-xs-12">
                <div class="input-group">
                    <input #couponCode type="text" style="text-transform: uppercase" class="form-control border border-secondary" placeholder="Coupon Code"
                        name="couponCode" [(ngModel)]="bookingRequest.couponCode">
                    <div class="input-group-append">
                      <button #applyBtn class="btn btn-secondary"  (click)="applyCoupon(couponCode, applyBtn, appliedText)" type="button">
                          Apply
                        </button>
                    </div>
                  </div>
            </div>
        </div>
    </div>

    <div class="mt-4" #appliedText style="display:none;">
        <a class="booknow" style="background-color: transparent;cursor: none;"><span style="color: green;"><b>{{ bookingRequest.couponCode | uppercase }}</b>applied successfully. You saved ₹{{ discount }}</span> &nbsp;&nbsp;&nbsp;<button style="margin-top: 2px;" (click)="rejectCoupon(couponCode, applyBtn, appliedText);" type="button" class="close" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button></a>
    </div>

    <div class="book-now mt-4" (click)="validateAndSubmit()">Pay Now</div>
</header>

<!-- Main -->
<div id="main" class="driverform bookingform">
    <section id="one">
        <header class="major">
            <h2 class="booking-heading">Booking Summary</h2>
        </header>
    </section>

    <section id="two">
        <span>Traveler Information</span>

        <div class="row">
            <div class="col-12 col-12-small whitebox">
                <form method="post" action="#">
                    <div class="row gtr-uniform gtr-50">
                        <div class="col-6">
                            <label>Name</label>
                            <input type="text" name="name" id="name" [(ngModel)]="bookingRequest.travelerName" />
                        </div>
                        
                        <div class="col-6">
                            <label>Email</label>
                            <input type="email" name="email" id="email" [(ngModel)]="bookingRequest.mailId" />
                        </div>
                        
                        <div class="col-6">
                            <label>Mobile Number</label>
                            <input type="text" name="mobile" id="mobile" [(ngModel)]="bookingRequest.phoneNumber" />
                        </div>
                        
                        <div class="col-6">
                            <label>Is it your What's App Number? </label>
                            
                            <!-- TODO Need to add field is whatsapp number in the request model and then bind to ngModel -->
                            <label class="switch">
                                <input type="checkbox">
                                <span class="slider round">Yes &nbsp; No</span>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            
            <div class="col-12 col-12-small bottombg" style="height: 50px;">

            </div>
        </div>
    </section>
    
    <div id="two" class="booking-review">
        <span>Travling & Billing Overview</span>
        <div class="row">
            <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="booking-invoice">
                    <div class="cabList">
                        <ul>
                            <li>
                                <div class="cabType">
                                    <img src="{{imageFolderPath}}/car.png">
                                </div>
                                <div class="cabInfo">
                                    <div class="modalType">
                                        <span class="d-block">{{bookingRequest?.carCategory}}</span>
                                        <small>{{bookingRequest?.carFeatures}}</small>
                                        <!-- <div class="price">Start at Rs. 2789</div> -->
                                    </div>
                                
                                    <div class="bookingDetail">
                                        <span><i class="fa fa-user"></i> &nbsp; {{bookingRequest?.carCapacity}} </span>
                                        <span><a class="fa fa-info-circle"></a></span>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <div class="add-information">
                        <div class="forminner">
                            <div class="form-group">
                                <span>
                                    <b>PICK UP ADDRESS</b>
                                    <br>
                                    {{bookingRequest?.pickUpAddress}}
                                </span>
                            </div>
                            <div class="form-group">
                                <span>
                                    <b>DROP OFF ADDRESS</b>
                                    <br>
                                    {{bookingRequest?.dropOffAddress}}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- <div class="add-information">
                        <div class="forminner">
                            <div class="form-group single-form">
                                <span>Toll of ₹ {{bookingRequest?.tollCharge}} Included.</span>
                            </div>
                        </div>
                    </div> -->
                </div>
            </div>

            <div class="col-sm-12 col-md-6 col-lg-6">
                <div class="booking-fare-table">
                    <div class="trip-detail">
                        <span>{{bookingRequest?.tripType}} Trip of {{bookingRequest?.duration}}.. {{bookingRequest?.distance}} Kms</span>
                        <div class="big-heading">₹ {{bookingRequest?.fare}}</div>
                    </div>

                    <div class="fare-row">
                        <span class="left-col">
                            <div class="b-head">Fare per Kms</div>
                        </span>                        
                        <span class="right-col t-right">₹ {{bookingRequest?.perKMCharges}}</span>
                    </div>

                    <div class="fare-row">
                        <span class="left-col">
                            <div class="b-head">Base Fare</div>
                            <span>{{bookingRequest?.fixRateNote}}</span>
                        </span>
                        <span class="right-col t-right">₹ {{bookingRequest?.basicFare}}</span>
                    </div>

                    <div class="fare-row">
                        <span class="left-col">
                            <div class="b-head">Taxes & Fees</div>
                        </span>
                        <span class="right-col t-right">₹ {{bookingRequest?.gst}}</span>
                    </div>

                    <!-- <div class="fare-row">
                        <span class="left-col">
                            <div class="b-head">Toll price	</div>
                        </span>
                        <span class="right-col t-right">₹ {{bookingRequest?.tollCharge}}</span>
                    </div> -->

                    <div class="fare-row">
                        <span class="left-col">
                            <div class="b-head">Discount price	</div>
                        </span>
                        <span class="right-col t-right">- ₹ {{bookingRequest?.couponDiscount}}</span>
                    </div>

                    <div class="total-fare">
                        <span class="left-col">
                            <div class="b-head">Total price	</div>
                        </span>
                        <span class="right-col t-right">₹ {{bookingRequest?.fare}}</span>
                    </div>
                </div>
            </div>

            <div class="col-sm-12 col-md-12 col-lg-12">
                <div class="terms-condition">
                    <h2>Terms & Conditions</h2>
                    <p>Parking, Airport Charges would be extra on actual basis and needs to be paid to authorities directly. Toll would be paid as per actual if not paid with booking.
                        Waiting charges @ Rs 200/hr. after 30 mins. (Billed on hourly basis)
                        Balance amount needs to be paid to driver at the beginning of the journey.
                        Only 1 break of 30 mins will be allowed during the journey.
                        AC would be switched off in hilly areas.
                        Any other charges levied be any Govt. body not mentioned under included needs to be paid on actual basis.
                        By confirming the booking you are agreeing with all our terms and conditions. 
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>