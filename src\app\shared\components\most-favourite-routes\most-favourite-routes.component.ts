import { Component, OnInit , Input, ViewChild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { HomeApiService } from '../../services/home-api.service';
import { FavouriteRoute } from '../../models/favourite-route.model';
import { AppConstants } from 'src/app/shared/constants/AppConstants';
import { ErrorMessage } from 'src/app/shared/constants/message.content';
import { SpinnerComponent } from '../spinner/spinner.component';
import { ToastrService } from 'ngx-toastr';
import { LoggerService } from '../../interceptors/logger.service';
import { GenericAPIArrayResponse } from '../../models/generic-api-response.model';
import { ResizeService } from '../../services/resize.service';
import { SCREEN_SIZE } from '../../constants/screen-size.enum';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-most-favourite-routes',
  templateUrl: './most-favourite-routes.component.html',
  styleUrls: ['./most-favourite-routes.component.css']
})
export class MostFavouriteRoutesComponent implements OnInit, OnDestroy {

  constructor(
    private homeAPIService: HomeApiService,
    private toastrService: ToastrService,
    private resizeService: ResizeService) { 
    this.logger = LoggerService.createLogger('MostFavouriteRoutesComponent');
    this.logger.debug('Inside Constructor');
  }

  private logger: LoggerService;

  favouriteRoutes: FavouriteRoute [];
  groupedFavouriteRoutes: FavouriteRoute[][];

  @Input() imageFolderPath: string;

  @ViewChild('spinner', { static: true })
  private spinner: SpinnerComponent;

  private currentScreenSize: SCREEN_SIZE = SCREEN_SIZE.LG;

  private resizeSubscription: Subscription;

  ngOnInit() { 
    this.initFields();
    this.subscribeResizeService();
    this.callFavouriteRoutesAPI();
  }

  ngOnDestroy() {
    if (this.resizeSubscription) {
      this.resizeSubscription.unsubscribe();
    }
  }

  private subscribeResizeService = () => {
    this.logger.trace('subscribeResizeService() called');
    this.resizeSubscription = this.resizeService.onResize().subscribe(
      (size: SCREEN_SIZE) => {
        this.logger.trace('listened to size in resizeService', size);
        this.currentScreenSize = size;
        this.logger.trace('currentScreenSize', this.currentScreenSize);

        this.logger.trace('favouriteRoutes', this.favouriteRoutes);
        if (this.favouriteRoutes) {
          this.groupFavouriteRoutes();
        }
      }
    );
  }

  private initFields = () => {
    this.favouriteRoutes = [];
    this.groupedFavouriteRoutes = [];
  }

  private callFavouriteRoutesAPI = () => {
    this.showSpinner();
    this.homeAPIService.getFavouriteRoutes().subscribe(
      (result: GenericAPIArrayResponse<FavouriteRoute>) => {
        this.hideSpinner();
        this.checkAPIResponse(result);
      }, (error) => {
        this.hideSpinner();
        this.logger.error('Error in callFavouriteRoutesAPI()', error);
        this.showFavouriteRouteError();
      }
    );
  }

  private checkAPIResponse = (result: GenericAPIArrayResponse<FavouriteRoute>) => {
    this.logger.trace('checkAPIResponse() called with result', result);

    if (result.succeeded) {
      const data = result.data;
      this.addFavouriteRoutes(data);
      this.groupFavouriteRoutes();
    }
  }

  private addFavouriteRoutes = (result: FavouriteRoute[]) => {
    this.logger.trace('addFavouriteRoute() called with result', result);
    result.forEach(result => this.favouriteRoutes.push(result));
    this.logger.trace('favouriteRoutes', this.favouriteRoutes);
  }

  private groupFavouriteRoutes = () => {
    this.logger.trace('groupFavouriteRoutes() called')

    this.logger.trace('favouriteRoutes', this.favouriteRoutes);
    const groupSize = this.getGroupSize();

    let favouriteRoutesTemp: FavouriteRoute[] = [];
    this.groupedFavouriteRoutes = [];
    let groupCounter = 0;

    this.favouriteRoutes.forEach(result => {
      if (groupCounter == groupSize) {
        groupCounter = 0;
        this.groupedFavouriteRoutes.push(favouriteRoutesTemp);
        favouriteRoutesTemp = [];
      }

      favouriteRoutesTemp.push(result);

      groupCounter++;
    });

    if (favouriteRoutesTemp.length > 0) {
      this.groupedFavouriteRoutes.push(favouriteRoutesTemp);
    }

    this.logger.trace('groupedFavouriteRoutes', this.groupedFavouriteRoutes);
  }

  private getGroupSize = (): number => {
    this.logger.trace('getGroupSize() called');
    this.logger.trace('currentScreenSize', this.currentScreenSize);

    const result = (this.currentScreenSize === SCREEN_SIZE.XS) ? 1 : AppConstants.FAVOURITE_ROUTE_GROUP_SIZE;
    this.logger.trace('getGroupSize() returning result', result);
    return result;
  }

  private showFavouriteRouteError = () => {
    this.toastrService.error(ErrorMessage.FAILED_FAVOURITE_ROUTES_GET, AppConstants.EMPTY_STRING);
  }

  private hideSpinner = () => {
    this.spinner.hide();
  }

  private showSpinner = () => {
    this.spinner.show();
  }
}
