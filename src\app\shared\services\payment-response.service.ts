import { BookingRequest } from './../models/booking.model';
import { PaymentResponse } from './../models/payment_response';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class PaymentResponseService {

  private paymentResponse: PaymentResponse;
  private bookingRequest: BookingRequest;

  constructor() { }

  get bookingRequestData() {
    return this.bookingRequest;
  }

  set bookingRequestData(obj) {
    this.bookingRequest = obj;
  }

  get paymentResponseValue() {
    return this.paymentResponse;
  }

  set paymentResponseValue(obj) {
    this.paymentResponse = obj;
  }

}
