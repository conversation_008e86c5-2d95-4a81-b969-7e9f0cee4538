import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { City } from '../models/city_model';
import { catchError } from 'rxjs/operators';
import { AppConfig } from 'src/app/configs/app.config';
import { PrivacyPolicy } from '../models/privacy_policy_model';
import { Service } from '../models/service_model';
import { CarCategoryResponse } from '../models/car-category-response';
import { BecomeDriver } from '../models/become-driver.model';
import { APIResponse } from '../models/api-response';
import { ContactUs } from '../models/contact-us.model';

@Injectable({
  providedIn: 'root'
})
export class CompanyGenericService {

  webAPIEndPoint: string = '';
  constructor(private httpClient: HttpClient) {
    this.webAPIEndPoint = AppConfig.CabYaari_WebAPI;
  }



  getCities(): Observable<Array<City>> {
    let apiURL = this.webAPIEndPoint + '/api/Home/cabyaari/cities';
    return this.httpClient.get<Array<City>>(
      apiURL
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    )
  }

  getServices(): Observable<Array<Service>> {
    let apiURL = this.webAPIEndPoint + '/api/Home/cabyaari/services';
    return this.httpClient.get<Array<Service>>(
      apiURL
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    )
  }

  getServicesDummy = (): Observable<Array<Service>> => {
    const service1 = new Service();
    service1.serviceName = 'Rapid City transfer';
    service1.serviceImagePath = 'city-transfer.png'
    service1.serviceIconClass = '';
    service1.briefIntroAboutService = 'We will bring you quickly and comfortably to anywhere in your city';
    service1.aboutService = 'We will bring you quickly and comfortably to anywhere in your city';

    const service2 = new Service();
    service2.serviceName = 'Booking a Hotel';
    service2.serviceImagePath = 'booking-hotel.png'
    service2.serviceIconClass = '';
    service2.briefIntroAboutService = 'We help to our customers to book best hotels.';
    service2.aboutService = 'We help to our customers to book best hotels.';

    const service3 = new Service();
    service3.serviceName = 'Air Transfer';
    service3.serviceImagePath = 'air-trasfer.png'
    service3.serviceIconClass = '';
    service3.briefIntroAboutService = 'We provide  Pick up and Drop off fecilities from Airports';
    service3.aboutService = 'We provide  Pick up and Drop off fecilities from Airports';

    const services = new Array();
    services.push(service1);
    services.push(service2);
    services.push(service3);

    services.push(service2);
    services.push(service3);
    services.push(service1);
    
    services.push(service3);
    services.push(service1);

    return of(services);
  }


  getCarCategoryRateFeatures(): Observable<CarCategoryResponse> {
    let apiURL = this.webAPIEndPoint + '/api/Home/cabyaari/carcategoryfeaturesrate';
    return this.httpClient.get<CarCategoryResponse>(
      apiURL
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    )
  }

  becomeDriver(driver: BecomeDriver): Observable<APIResponse> {
    let apiURL = this.webAPIEndPoint + '/api/Home/cabyaari/driver';
    return this.httpClient.post<APIResponse>(
      apiURL, driver
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    )
  }

  contactUs(contact: ContactUs): Observable<APIResponse> {
    let apiURL = this.webAPIEndPoint + '/api/Home/cabyaari/contact';
    return this.httpClient.post<APIResponse>(
      apiURL, contact
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    )
  }


}
