import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { PickUpDropOffRequest } from '../models/pick-drop-request.model';
import { LoggerService } from '../interceptors/logger.service';

@Injectable({
  providedIn: 'root'
})
export class BookingDetailsSharedService {

  private pickupDropoffRequest: BehaviorSubject<PickUpDropOffRequest>;

  constructor() { 
    this.logger = LoggerService.createLogger('BookingDetailsSharedService');
  }

  private logger: LoggerService;

  set = (request: PickUpDropOffRequest) => {
    this.logger.trace('BookingDetailsSharedService:: set() called with request', request);
    if (!this.pickupDropoffRequest) {
      this.pickupDropoffRequest = new BehaviorSubject<PickUpDropOffRequest>(request);
    }
    this.pickupDropoffRequest.next(request);
  }

  get = (): BehaviorSubject<PickUpDropOffRequest> => {
    return this.pickupDropoffRequest;
  }

  getData = (): PickUpDropOffRequest => {
    if (this.pickupDropoffRequest) {
      return this.pickupDropoffRequest.value;
    } else {
      return null;
    }
  }
}
