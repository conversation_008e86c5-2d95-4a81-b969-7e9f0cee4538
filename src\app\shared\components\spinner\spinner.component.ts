import { Component } from '@angular/core';
import { Subject, BehaviorSubject } from 'rxjs';

@Component({
  selector: 'app-spinner',
  templateUrl: './spinner.component.html',
  styleUrls: ['./spinner.component.css']
})
export class SpinnerComponent {

  constructor() { }

  // hideFlag: Subject<boolean> = new BehaviorSubject(true);
  hideFlag: boolean = true;
  
  hide = () => {
    // this.hideFlag.next(true);
    this.hideFlag = true;
  }

  show = () => {
    // this.hideFlag.next(false);
    this.hideFlag = false;
  }

}
