import { Injectable } from '@angular/core';
import { Observable, from, throwError } from 'rxjs';

export interface GeolocationPosition {
  latitude: number;
  longitude: number;
  accuracy: number;
}

@Injectable({
  providedIn: 'root'
})
export class GeolocationService {

  constructor() { }

  /**
   * Get current position using browser's geolocation API
   * @returns Observable<GeolocationPosition>
   */
  getCurrentPosition(): Observable<GeolocationPosition> {
    if (!navigator.geolocation) {
      return throwError('Geolocation is not supported by this browser.');
    }

    return from(
      new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            resolve({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy
            });
          },
          (error) => {
            let errorMessage = 'Unknown error occurred';
            switch (error.code) {
              case error.PERMISSION_DENIED:
                errorMessage = 'Location access denied by user';
                break;
              case error.POSITION_UNAVAILABLE:
                errorMessage = 'Location information is unavailable';
                break;
              case error.TIMEOUT:
                errorMessage = 'Location request timed out';
                break;
            }
            reject(errorMessage);
          },
          {
            enableHighAccuracy: false, // Use false for faster response
            timeout: 5000, // Shorter timeout for auto-detection
            maximumAge: 600000 // 10 minutes cache
          }
        );
      })
    );
  }

  /**
   * Check if geolocation is supported
   * @returns boolean
   */
  isGeolocationSupported(): boolean {
    return 'geolocation' in navigator;
  }
}
