<!-- Main -->
<div id="main" class="user-rides">

    <!-- Header Section -->
    <section id="header-section">
        <div class="header-content">
            <h2>Your Rides</h2>
            <button class="refresh-btn" (click)="refreshBookings()" [disabled]="isLoading">
                <i class="fa fa-refresh" [class.fa-spin]="isLoading"></i>
                Refresh
            </button>
        </div>
    </section>

    <!-- Loading State -->
    <div *ngIf="isLoading && bookings.length === 0" class="loading-container">
        <div class="spinner">
            <i class="fa fa-spinner fa-spin fa-2x"></i>
        </div>
        <p>Loading your bookings...</p>
    </div>

    <!-- Error State -->
    <div *ngIf="hasError" class="error-container">
        <div class="error-content">
            <i class="fa fa-exclamation-triangle fa-2x"></i>
            <h3>Oops! Something went wrong</h3>
            <p>{{ errorMessage }}</p>
            <button class="retry-btn" (click)="refreshBookings()">
                <i class="fa fa-refresh"></i>
                Try Again
            </button>
        </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoading && !hasError && bookings.length === 0" class="empty-container">
        <div class="empty-content">
            <i class="fa fa-car fa-3x"></i>
            <h3>No rides found</h3>
            <p>You haven't made any bookings yet. Start your journey with CabYaari!</p>
            <button class="book-now-btn" routerLink="/home">
                <i class="fa fa-plus"></i>
                Book Your First Ride
            </button>
        </div>
    </div>

    <!-- Bookings List -->
    <section *ngIf="!isLoading && !hasError && bookings.length > 0" id="bookings-section">

        <!-- Summary -->
        <div class="bookings-summary">
            <div class="summary-item">
                <span class="summary-label">Total Bookings:</span>
                <span class="summary-value">{{ totalCount }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Showing:</span>
                <span class="summary-value">{{ bookings.length }} of {{ totalCount }}</span>
            </div>
        </div>

        <!-- Bookings Grid -->
        <div class="bookings-grid">
            <div *ngFor="let booking of bookings; trackBy: trackByBookingId"
                 class="booking-card"
                 (click)="viewBookingDetails(booking.bookingId)">

                <!-- Booking Header -->
                <div class="booking-header">
                    <div class="booking-id">
                        <i class="fa fa-ticket-alt"></i>
                        {{ booking.bookingId }}
                    </div>
                    <div class="booking-status">
                        <span class="status-badge" [ngClass]="getStatusClass(booking.razorpayStatus)">
                            {{ booking.razorpayStatus }}
                        </span>
                    </div>
                </div>

                <!-- Travel Route -->
                <div class="travel-route">
                    <div class="route-cities">
                        <div class="city from-city">
                            <i class="fa fa-map-marker-alt"></i>
                            <span>{{ booking.pickUpCity }}</span>
                        </div>
                        <div class="route-arrow">
                            <i class="fa fa-arrow-right"></i>
                        </div>
                        <div class="city to-city">
                            <i class="fa fa-map-marker-alt"></i>
                            <span>{{ booking.dropOffCity }}</span>
                        </div>
                    </div>
                    <div class="trip-type">
                        <span class="trip-badge">{{ booking.tripType }}</span>
                    </div>
                </div>

                <!-- Booking Details -->
                <div class="booking-details">
                    <div class="detail-row">
                        <div class="detail-item">
                            <i class="fa fa-rupee-sign"></i>
                            <span class="detail-label">Fare:</span>
                            <span class="detail-value">₹{{ booking.fare | number:'1.0-0' }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fa fa-car"></i>
                            <span class="detail-label">Vehicle:</span>
                            <span class="detail-value">{{ booking.carCategory }}</span>
                        </div>
                    </div>
                    <div class="detail-row">
                        <div class="detail-item">
                            <i class="fa fa-calendar-alt"></i>
                            <span class="detail-label">Date:</span>
                            <span class="detail-value">{{ formatDate(booking.pickUpDate) }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fa fa-clock"></i>
                            <span class="detail-label">Time:</span>
                            <span class="detail-value">{{ formatTime(booking.pickUpTime) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Traveler Info -->
                <div class="traveler-info">
                    <div class="traveler-name">
                        <i class="fa fa-user"></i>
                        {{ booking.travelerName }}
                    </div>
                    <div class="traveler-phone">
                        <i class="fa fa-phone"></i>
                        {{ booking.phoneNumber }}
                    </div>
                </div>

                <!-- Card Footer -->
                <div class="card-footer">
                    <div class="view-details">
                        <span>View Details</span>
                        <i class="fa fa-chevron-right"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Load More Button -->
        <div *ngIf="hasMoreResults" class="load-more-container">
            <button class="load-more-btn"
                    (click)="loadMoreBookings()"
                    [disabled]="isLoadingMore">
                <i class="fa fa-spinner fa-spin" *ngIf="isLoadingMore"></i>
                <i class="fa fa-plus" *ngIf="!isLoadingMore"></i>
                {{ isLoadingMore ? 'Loading...' : 'Load More Bookings' }}
            </button>
        </div>

        <!-- End of Results -->
        <div *ngIf="!hasMoreResults && bookings.length > 0" class="end-of-results">
            <p>You've reached the end of your bookings</p>
        </div>

    </section>
</div>