import { AppConstants } from './AppConstants';

export const ValidationMessages=
{
    PICK_UP_CITY: 'Please enter pick up city !',
    DROP_OFF_CITY: 'Please enter drop off city !',
    SAME_PICKUP_DROPOFF_CITY: 'Pickup and drop-off cities cannot be the same !',
    INVALID_PICKUP_CITY_SELECTION: 'Please select pickup city from the suggestions !',
    INVALID_DROPOFF_CITY_SELECTION: 'Please select drop-off city from the suggestions !',
    INVALID_PICKUP_ADDRESS: 'Invalid pick up address',
    PICK_UP_CITY_ADDRESS: 'Please select pick up city address !',
    INVALID_DROPOFF_ADDRESS: 'Invalid drop off address',
    DROP_OFF_CITY_ADDRESS: 'Please select drop off city address!',
    PICKUP_DATE: 'Please select journay start date !',
    VALID_PICKUP_DATE: 'Please select valid journay start date !',
    INVALID_PICKUP_DATE: 'Invalid pick up date !',
    SELECT_TIME: 'Please select pick up time !',
    VALID_START_TIME: 'Please select valid journay start time !',
    INVALID_START_TIME: 'Invalid start time !',
    PHONE_NUMBER: 'Please enter phone number !',
    VALID_PHONE_NUMBER: 'Please enter phone number !',
    EMAIL_ID: 'Please enter phone number !',
    VALID_EMAIL_ID: 'Please enter phone number !',
    FISRT_NAME:'Please enter first name !',
    LAST_NAME:'Please enter last name !',
    NAME:'Please enter your name !',
    ADDRESS:'Please enter your address !',
    QUERY:'Please enter your address !',
    INAVLID_LOGIN_INFO: 'Invalid Login Details,Please enter valid details',
    PAST_PICKUP_DATE_TIME: 'Past Date and Time is not allowed for booking!',
    PICKUP_DATE_LIMIT: 'Pickup Date cannot exceed ' + AppConstants.FUTURE_BOOKING_DAYS_LIMIT + ' days !',
    INVALID_PICKUP_DATE_TIME: 'Invalid pickup date and time !',
    CAR_CATEGORY_REQUIRED: 'Car category is required !',
    SELECT_CAR_CATEGORY: 'Please select a car category !',
    ENTER_FIRST_NAME: 'Please enter First Name!',
    ENTER_LAST_NAME: 'Please enter Last Name!',
    ENTER_EMAIL: 'Please enter Email ID!',
    ENTER_PHONE: 'Please enter Phone number!',
    ENTER_USERNAME: 'Please enter username!',
    ENTER_PASSWORD: 'Please enter password!',
    PASSWORD_CONFIRM_PASSWORD_NOT_MATCH: 'Password and confirm password are different!',
    CONTACT_LENGTH: 'Phone number should be of 10 digits.',
    USERNAME_SPACE_ERROR: 'Username should not contain spaces.',
    PASSWORD_VALIDATION: 'Password must contain atleast 1 uppercase, 1 lowercase, 1 numeral and 1 special character and must be between 8-15 characters.'
}

export const ErrorMessage = {
    ROUTE_API_ERROR: 'Unable to get route!',
    PICKUP_DROPOFF_CAR_CATEGORY_API_ERROR: 'Unable to get cars for the given locations!',
    REGISTRATION_NOT_WORKING: 'Sign Up feature is not working right now.',
    REGISTRATION_FAILED: 'Registration Failed!',
    BOOKING_DETAIL_FAILED: 'Failed to get Booking Details!',
    BOOKING_FAILED: 'Booking Failed!',
    TRY_AFTER_SOME_TIME: 'Try After sometime',
    FAILED_FAVOURITE_ROUTES_GET: 'Failed to Get Favourite Routes!',
    MIN_FARE_COUPON: 'To avail this discount, minimum Fare should be greater than or equal to 2000.',
    EMPTY_COUPON: 'Kindly provide the coupon code.',
    COUPON_NOT_VALID: 'Coupon code not applicable.',
    PAYMENT_FAILED: 'Payment failed. Please try again.'
}

export const SuccessMessage = {
    REGISTRATION_SUCCESSFULL: 'Registration done successfully.',
    BOOKING_SUCCESSFULL: 'Booking created.',
    COUPON_APPLIED: 'Coupon applied successfully.'
}