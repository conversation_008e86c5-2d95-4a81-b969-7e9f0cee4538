import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { MMISuggestedModel} from '../../models/mmi/mmi_suggested_locations_model';
import { City } from '../../models/city_model';
import { AppConfig } from 'src/app/configs/app.config';
import { suggestedLocations } from '../../models/suggested-booking-categories.model';
import { ETA } from '../../models/mmi/mmt_eta_model';
import { CarCategory } from '../../models/car-category.model';


@Injectable({
  providedIn: 'root'
})
export class MmiService {

  webAPIEndPoint: string = '';
  webAPIEndPointNew: string = '';
  constructor(private httpClient: HttpClient) {
    this.webAPIEndPoint = AppConfig.CabYaari_WebAPI;
    this.webAPIEndPointNew = AppConfig.CabYaari_WebAPI_New;
  }

   private grantType = 'client_credentials';
  private clientId = 'fZqsZfvayHDeoMKEwvq1oa5lJvOJb0s7ByZio2GKFKR3CHrYqZAlxgSwdVXqfUdAafuZVGYkxNlZ2KTY-0Gw_g==';
  private clientSecret = '9K_q_9Q2GHP3JWfb2e8giYxXMbpCGf6UHJxp19Tkw4cv8-p5wwzP8amxlGUEKg4-flfQKMyVuMwZ02xgI49Ej71luFNwWORd';
  
  // tslint:disable-next-line: max-line-length
  private securityURL = this.webAPIEndPoint+'/api/MMIServices/token';
  private autoSuggestURL = this.webAPIEndPoint+'/api/mmiservices/autosuggest?placeName=';
  private nearbyURL = this.webAPIEndPoint+'/api/places/nearby/json?explain&richData&username=balmukand&refLocation=28.467470,77.077518&keywords=FINATM';
  private geocodeURL = this.webAPIEndPoint+'/api/places/geocode?address=mapmyindia 237 okhla phase 3';
  private textsearchURL = this.webAPIEndPoint+'/api/MMIServices/textsearch?placeName=';

  private baseURL = 'https://outpost.mapmyindia.com/api/security/oauth/';

  getToken() {
    let promise = new Promise((resolve, reject) => {
        let apiURL = this.webAPIEndPoint+'/api/MMIServices/token';
        this.httpClient.post(apiURL, null)
          .toPromise()
          .then(
          res => { // Success
              resolve(res);
              console.log(res)
            }
        );
    });
    return promise;
  }

  getCarFeatures(): Observable<Array<CarCategory>> {
    let apiURL = this.webAPIEndPoint+'/api/Home/cabyaari/carcategoryfeaturesrate';
    return this.httpClient.get<Array<CarCategory>>(
      apiURL
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    )
  } 

  getCarCategoryFare(pickUpAddressLongLat:string,dropOffAddressLongLat:string,tripType:string): Observable<any> {
    let apiURL = this.webAPIEndPointNew+'/api/v1/Booking/GetFareDetails?pickUpAddressLongLat='+pickUpAddressLongLat+'&dropOffAddressLongLat='+dropOffAddressLongLat+'&tripType='+tripType;
    return this.httpClient.get<any>(
      apiURL,
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    )
  } 

  geCities(): Observable<Array<City>> {
    let apiURL = this.webAPIEndPoint+'/api/Home/cabyaari/cities';
    return this.httpClient.get<Array<City>>(
      apiURL
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    )
  }

  textsearch(placeName:string,token: string,eloc:string) {
    const _url = this.textsearchURL+placeName+"&token="+token+"&eloc="+eloc;
    const promise = new Promise((resolve, reject) => {
      this.httpClient.get(_url)
        .toPromise()
        .then(
            res => { // Success
                resolve(res);
            }
        );
    });
    return promise ;
  }

  autoSuggest(placeName:string,token: string,eloc:string): Observable<any>
   {
    const _url = this.webAPIEndPoint+"/api/MMIServices/autosuggest?"+"placeName="+placeName+"&token="+token+"&eloc="+eloc;
    return this.httpClient.get<any>(
      _url
      ).pipe(
        catchError((error : HttpErrorResponse)=>
        {
          console.log("autoSuggestError",error.message);
           throw Error('somethin went wrong autoSuggestError')
        }
        )
      );
  }
  

  // autoSuggest(placeName:string,token: string) {
  //   //{headers: new HttpHeaders().set('Authorization', 'bearer '+token+'')}
  //       const _url = this.autoSuggestURL+placeName+"&token="+token;
  //       const promise = new Promise((resolve, reject) => {
  //         this.httpClient.get(_url)
  //           .toPromise()
  //           .then(
  //               res => { // Success
  //                   // console.log(res);
  //                   resolve(res);
  //               }
  //           );
  //       });
  //       return promise;
  //   }

  
//   autoSuggest(placeName:string,token: string) {
// //{headers: new HttpHeaders().set('Authorization', 'bearer '+token+'')}
//     const _url = this.autoSuggestURL+placeName+"&token="+token;
//     const promise = new Promise((resolve, reject) => {
//       this.httpClient.get(_url)
//         .toPromise()
//         .then(
//             res => { // Success
//                 // console.log(res);
//                 resolve(res);
//             }
//         );
//     });
//     return promise;
// }

  nearby(token: string) {
    const _url = this.nearbyURL+"&token="+token;
    const promise = new Promise((resolve, reject) => {
      this.httpClient.get(_url)
        .toPromise()
        .then(
            res => { // Success
                resolve(res);
            }
        );
    });
    return promise;
  }

  geocode(token: string) {
    const _url = this.geocodeURL+"placeName=jaipur&token="+token;
    const promise = new Promise((resolve, reject) => {
      this.httpClient.get(_url)
        .toPromise()
        .then(
            res => { // Success
                resolve(res);
            }
        );
    });
    return promise;
  }


  jsonp(uri) {
    return new Promise(function(resolve, reject) {
      const id = '_' + Math.round(10000 * Math.random());
        const callbackName = 'jsonp_callback_' + id;
        window[callbackName] = function(data) {
            delete window[callbackName];
            const ele = document.getElementById(id);
            ele.parentNode.removeChild(ele);
            resolve(data);
        };
        const src = uri + '&callback=' + callbackName;
        const script = document.createElement('script');
        script.src = src;
        script.id = id;
        script.addEventListener('error', reject);
        (document.getElementsByTagName('head')[0] || document.body || document.documentElement).appendChild(script);
    });
  }

  getETA = (srcLat: string, srcLng: string, dstLat: string, dstLng: string) => {
    console.log('getETA() called with srcLat: ', srcLat, ', srcLng: ', srcLng, ', dstLat: ', dstLat, ', dstLng: ', dstLng);

    const apiURL = this.webAPIEndPoint+'/api/MMIServices/eta';
    const fromLatLong = srcLng + ',' + srcLat;
    const toLatLong = dstLng + ',' + dstLat;
    console.log('apiURL', apiURL, ', fromLatLong', fromLatLong, ', toLatLong', toLatLong);

    let queryParams: HttpParams = new HttpParams();
    queryParams = queryParams.append('fromLatLong', fromLatLong);
    queryParams = queryParams.append('toLatLong',  toLatLong);
    return this.httpClient.get<ETA>(apiURL, { params: queryParams });
  }

  // Reverse geocoding to get city name from coordinates
  reverseGeocode = (lat: number, lng: number, token: string): Observable<any> => {
    console.log('reverseGeocode() called with lat: ', lat, ', lng: ', lng);

    const apiURL = this.webAPIEndPoint + '/api/MMIServices/reverseGeocode';
    let queryParams: HttpParams = new HttpParams();
    queryParams = queryParams.append('lat', lat.toString());
    queryParams = queryParams.append('lng', lng.toString());
    queryParams = queryParams.append('token', token);

    return this.httpClient.get<any>(apiURL, { params: queryParams }).pipe(
      catchError((error: HttpErrorResponse) => {
        console.log('reverseGeocode error', error);
        throw error;
      })
    );
  }

}
