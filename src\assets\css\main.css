@import url("fontawesome-all.min.css");
@import url("https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400italic");

/*
	Strata by HTML5 UP
	html5up.net | @ajlkn
	Free for personal and commercial use under the CCA 3.0 license (html5up.net/license)
*/

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}

.carousel-inner .carousel-item {
	transition: -webkit-transform 0.5s ease;
	transition: transform 0.5s ease;
	transition: transform 0.5s ease, -webkit-transform 0.5s ease;
}


article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
	display: block;
}

body {
	line-height: 1;
}

*:focus {
	outline: none;
}

ol,
ul {
	list-style: none;
}

blockquote,
q {
	quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
	content: '';
	content: none;
}


.toolTipClass {
	border: 1px solid #000;
}

.duration {
	color: green;
	font-weight: bold;
}

.distance {
	font-weight: lighter;
}

.leaflet-tooltip-left.toolTipClass::before {
	border-left-color: #000;
}

.leaflet-tooltip-right.toolTipClass::before {
	border-right-color: #000;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

body {
	-webkit-text-size-adjust: none;
}

mark {
	background-color: transparent;
	color: inherit;
}

input::-moz-focus-inner {
	border: 0;
	padding: 0;
}

input,
select,
textarea {
	-moz-appearance: none;
	-webkit-appearance: none;
	-ms-appearance: none;
	appearance: none;
}

/* Basic */

html {
	box-sizing: border-box;
}

*,
*:before,
*:after {
	box-sizing: inherit;
}

body {
	background: #fff;
}

body.is-preload *,
body.is-preload *:before,
body.is-preload *:after {
	-moz-animation: none !important;
	-webkit-animation: none !important;
	-ms-animation: none !important;
	animation: none !important;
	-moz-transition: none !important;
	-webkit-transition: none !important;
	-ms-transition: none !important;
	transition: none !important;
}

body,
input,
select,
textarea {
	color: #a2a2a2;
	font-family: "Roboto", "Source Sans Pro", Helvetica, sans-serif;
	font-size: 16pt;
	font-weight: 400;
	line-height: 1.75em;
}

a {
	-moz-transition: color 0.2s ease-in-out, border-color 0.2s ease-in-out;
	-webkit-transition: color 0.2s ease-in-out, border-color 0.2s ease-in-out;
	-ms-transition: color 0.2s ease-in-out, border-color 0.2s ease-in-out;
	transition: color 0.2s ease-in-out, border-color 0.2s ease-in-out;
	color: #ffed4d;
	text-decoration: none;
}

a:hover {
	border-bottom-color: transparent;
	color: #ffed4d !important;
	text-decoration: none;
}

strong,
b {
	color: #787878;
	font-weight: 400;
}

em,
i {
	font-style: italic;
}

p {
	margin: 0 0 2em 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	color: #787878;
	font-weight: 400;
	line-height: 1em;
	margin: 0 0 1em 0;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
	color: inherit;
	text-decoration: none;
}

h1 {
	font-size: 2em;
	line-height: 1.5em;
}

h2 {
	font-size: 1.5em;
	line-height: 1.5em;
}

h3 {
	font-size: 1.25em;
	line-height: 1.5em;
}

h4 {
	font-size: 1.1em;
	line-height: 1.5em;
}

h5 {
	font-size: 0.9em;
	line-height: 1.5em;
}

h6 {
	font-size: 0.7em;
	line-height: 1.5em;
}

sub {
	font-size: 0.8em;
	position: relative;
	top: 0.5em;
}

sup {
	font-size: 0.8em;
	position: relative;
	top: -0.5em;
}

hr {
	border: 0;
	border-bottom: solid 2px #efefef;
	margin: 2em 0;
}

hr.major {
	margin: 3em 0;
}

blockquote {
	border-left: solid 6px #efefef;
	font-style: italic;
	margin: 0 0 2em 0;
	padding: 0.5em 0 0.5em 1.5em;
}

code {
	background: #f7f7f7;
	border-radius: 0.35em;
	border: solid 2px #efefef;
	font-family: "Courier New", monospace;
	font-size: 0.9em;
	margin: 0 0.25em;
	padding: 0.25em 0.65em;
}

pre {
	-webkit-overflow-scrolling: touch;
	font-family: "Courier New", monospace;
	font-size: 0.9em;
	margin: 0 0 2em 0;
}

pre code {
	display: block;
	line-height: 1.75em;
	padding: 1em 1.5em;
	overflow-x: auto;
}

.align-left {
	text-align: left;
}

.align-center {
	text-align: center;
}

.align-right {
	text-align: right;
}

/* Container */

.container {
	margin: 0 auto;
	max-width: calc(100% - 4em);
	width: 100%;
}

.container.xsmall {
	width: 25%;
}

.container.small {
	width: 50%;
}

.container.medium {
	width: 75%;
}

.container.large {
	width: 125%;
}

.container.xlarge {
	width: 150%;
}

.container.max {
	width: 100%;
}

@media screen and (max-width: 980px) {

	.container {
		width: 100% !important;
		max-width: 100% !important;
	}

	/* #main #one, #main #two, #main #four{
				padding-left: 0px;
				padding-right: 0px;
			} */

	#main #one,
	#main #two {
		padding-left: 0px;
		padding-right: 0px;
	}


}

@media screen and (max-width: 480px) {
	#main {
		background-image: none;
	}

	.container {
		max-width: calc(100% - 3em);
	}

}

/* Row */

.row {
	display: flex;
	flex-wrap: wrap;
	box-sizing: border-box;
	align-items: stretch;
}

.row>* {
	box-sizing: border-box;
}

.row.gtr-uniform>*> :last-child {
	margin-bottom: 0;
}

.row.aln-left {
	justify-content: flex-start;
}

.row.aln-center {
	justify-content: center;
}

.row.aln-right {
	justify-content: flex-end;
}

.row.aln-top {
	align-items: flex-start;
}

.row.aln-middle {
	align-items: center;
}

.row.aln-bottom {
	align-items: flex-end;
}

.row>.imp {
	order: -1;
}

.row>.col-1 {
	width: 8.33333%;
}

.row>.off-1 {
	margin-left: 8.33333%;
}

.row>.col-2 {
	width: 16.66667%;
}

.row>.off-2 {
	margin-left: 16.66667%;
}

.row>.col-3 {
	width: 25%;
}

.row>.off-3 {
	margin-left: 25%;
}

.row>.col-4 {
	width: 33.33333%;
}

.row>.off-4 {
	margin-left: 33.33333%;
}

.row>.col-5 {
	width: 41.66667%;
}

.row>.off-5 {
	margin-left: 41.66667%;
}

.row>.col-6 {
	width: 33.33%;
}

.row>.off-6 {
	margin-left: 50%;
}

.row>.col-7 {
	width: 58.33333%;
}

.row>.off-7 {
	margin-left: 58.33333%;
}

.row>.col-8 {
	width: 66.66667%;
}

.row>.off-8 {
	margin-left: 66.66667%;
}

.row>.col-9 {
	width: 75%;
}

.row>.off-9 {
	margin-left: 75%;
}

.row>.col-10 {
	width: 83.33333%;
}

.row>.off-10 {
	margin-left: 83.33333%;
}

.row>.col-11 {
	width: 91.66667%;
}

.row>.off-11 {
	margin-left: 91.66667%;
}

.row>.col-12 {
	width: 100%;
}

.row>.off-12 {
	margin-left: 100%;
}

.row.gtr-0 {
	margin-top: 0;
	margin-left: 0em;
}

.row.gtr-0>* {
	padding: 0 0 0 0em;
}

.row.gtr-0.gtr-uniform {
	margin-top: 0em;
}

.row.gtr-0.gtr-uniform>* {
	padding-top: 0em;
}

.row.gtr-25 {
	margin-top: 0;
	margin-left: -0.625em;
}

.row.gtr-25>* {
	padding: 0 0 0 0.625em;
}

.row.gtr-25.gtr-uniform {
	margin-top: -0.625em;
}

.row.gtr-25.gtr-uniform>* {
	padding-top: 0.625em;
}

.row.gtr-50 {
	margin-top: 0;
	margin-left: -1.25em;
}

.row.gtr-50>* {
	padding: 0 0 0 1.25em;
}

.row.gtr-50.gtr-uniform {
	margin-top: -1.25em;
}

.row.gtr-50.gtr-uniform>* {
	padding-top: 1.25em;
}

.row {
	margin-top: 0;
	margin-left: -2.5em;
}

.row>* {
	padding: 0 0 0 2.5em;
}

.row.gtr-uniform {
	margin-top: -2.5em;
}

.row.gtr-uniform>* {
	padding-top: 2.5em;
}

.row.gtr-150 {
	margin-top: 0;
	margin-left: -3.75em;
}

.row.gtr-150>* {
	padding: 0 0 0 3.75em;
}

.row.gtr-150.gtr-uniform {
	margin-top: -3.75em;
}

.row.gtr-150.gtr-uniform>* {
	padding-top: 3.75em;
}

.row.gtr-200 {
	margin-top: 0;
	margin-left: -5em;
}

.row.gtr-200>* {
	padding: 0 0 0 5em;
}

.row.gtr-200.gtr-uniform {
	margin-top: -5em;
}

.row.gtr-200.gtr-uniform>* {
	padding-top: 5em;
}

@media screen and (max-width: 1800px) {

	.row {
		display: flex;
		flex-wrap: wrap;
		box-sizing: border-box;
		align-items: stretch;
		margin: 0px;
	}

	.row>* {
		box-sizing: border-box;
	}

	.row.gtr-uniform>*> :last-child {
		margin-bottom: 0;
	}

	.row.aln-left {
		justify-content: flex-start;
	}

	.row.aln-center {
		justify-content: center;
	}

	.row.aln-right {
		justify-content: flex-end;
	}

	.row.aln-top {
		align-items: flex-start;
	}

	.row.aln-middle {
		align-items: center;
	}

	.row.aln-bottom {
		align-items: flex-end;
	}

	.row>.imp-xlarge {
		order: -1;
	}

	.row>.col-1-xlarge {
		width: 8.33333%;
	}

	.row>.off-1-xlarge {
		margin-left: 8.33333%;
	}

	.row>.col-2-xlarge {
		width: 16.66667%;
	}

	.row>.off-2-xlarge {
		margin-left: 16.66667%;
	}

	.row>.col-3-xlarge {
		width: 25%;
	}

	.row>.off-3-xlarge {
		margin-left: 25%;
	}

	.row>.col-4-xlarge {
		width: 33.33333%;
	}

	.row>.off-4-xlarge {
		margin-left: 33.33333%;
	}

	.row>.col-5-xlarge {
		width: 41.66667%;
	}

	.row>.off-5-xlarge {
		margin-left: 41.66667%;
	}

	.row>.col-6-xlarge {
		width: 50%;
	}

	.row>.off-6-xlarge {
		margin-left: 50%;
	}

	.row>.col-7-xlarge {
		width: 58.33333%;
	}

	.row>.off-7-xlarge {
		margin-left: 58.33333%;
	}

	.row>.col-8-xlarge {
		width: 66.66667%;
	}

	.row>.off-8-xlarge {
		margin-left: 66.66667%;
	}

	.row>.col-9-xlarge {
		width: 75%;
	}

	.row>.off-9-xlarge {
		margin-left: 75%;
	}

	.row>.col-10-xlarge {
		width: 83.33333%;
	}

	.row>.off-10-xlarge {
		margin-left: 83.33333%;
	}

	.row>.col-11-xlarge {
		width: 91.66667%;
	}

	.row>.off-11-xlarge {
		margin-left: 91.66667%;
	}

	.row>.col-12-xlarge {
		width: 100%;
	}

	.row>.off-12-xlarge {
		margin-left: 100%;
	}

	.row.gtr-0 {
		margin-top: 0;
		margin-left: 0em;
	}

	.row.gtr-0>* {
		padding: 0 0 0 0em;
	}

	.row.gtr-0.gtr-uniform {
		margin-top: 0em;
	}

	.row.gtr-0.gtr-uniform>* {
		padding-top: 0em;
	}

	.row.gtr-25 {
		margin-top: 0;
		margin-left: -0.625em;
	}

	.row.gtr-25>* {
		padding: 0 0 0 0.625em;
	}

	.row.gtr-25.gtr-uniform {
		margin-top: -0.625em;
	}

	.row.gtr-25.gtr-uniform>* {
		padding-top: 0.625em;
	}

	.row.gtr-50 {
		margin-top: 0;
		margin-left: -1.25em;
	}

	.row.gtr-50>* {
		padding: 0 0 0 1.25em;
	}

	.row.gtr-50.gtr-uniform {
		margin-top: -1.25em;
	}

	.row.gtr-50.gtr-uniform>* {
		padding-top: 1.25em;
	}

	.row {
		margin-top: 0;
		margin-left: -2em;
	}

	.row>* {
		padding: 0 0 0 2em;
	}

	.row.gtr-uniform {
		margin-top: -2.5em;
	}

	.row.gtr-uniform>* {
		padding-top: 2.5em;
	}

	.row.gtr-150 {
		margin-top: 0;
		margin-left: -3.75em;
	}

	.row.gtr-150>* {
		padding: 0 0 0 3.75em;
	}

	.row.gtr-150.gtr-uniform {
		margin-top: -3.75em;
	}

	.row.gtr-150.gtr-uniform>* {
		padding-top: 3.75em;
	}

	.row.gtr-200 {
		margin-top: 0;
		margin-left: -5em;
	}

	.row.gtr-200>* {
		padding: 0 0 0 5em;
	}

	.row.gtr-200.gtr-uniform {
		margin-top: -5em;
	}

	.row.gtr-200.gtr-uniform>* {
		padding-top: 5em;
	}

}

@media screen and (max-width: 1280px) {

	.row {
		display: flex;
		flex-wrap: wrap;
		box-sizing: border-box;
		align-items: stretch;
	}

	.row>* {
		box-sizing: border-box;
	}

	.row.gtr-uniform>*> :last-child {
		margin-bottom: 0;
	}

	.row.aln-left {
		justify-content: flex-start;
	}

	.row.aln-center {
		justify-content: center;
	}

	.row.aln-right {
		justify-content: flex-end;
	}

	.row.aln-top {
		align-items: flex-start;
	}

	.row.aln-middle {
		align-items: center;
	}

	.row.aln-bottom {
		align-items: flex-end;
	}

	.row>.imp-large {
		order: -1;
	}

	.row>.col-1-large {
		width: 8.33333%;
	}

	.row>.off-1-large {
		margin-left: 8.33333%;
	}

	.row>.col-2-large {
		width: 16.66667%;
	}

	.row>.off-2-large {
		margin-left: 16.66667%;
	}

	.row>.col-3-large {
		width: 25%;
	}

	.row>.off-3-large {
		margin-left: 25%;
	}

	.row>.col-4-large {
		width: 33.33333%;
	}

	.row>.off-4-large {
		margin-left: 33.33333%;
	}

	.row>.col-5-large {
		width: 41.66667%;
	}

	.row>.off-5-large {
		margin-left: 41.66667%;
	}

	.row>.col-6-large {
		width: 50%;
	}

	.row>.off-6-large {
		margin-left: 50%;
	}

	.row>.col-7-large {
		width: 58.33333%;
	}

	.row>.off-7-large {
		margin-left: 58.33333%;
	}

	.row>.col-8-large {
		width: 66.66667%;
	}

	.row>.off-8-large {
		margin-left: 66.66667%;
	}

	.row>.col-9-large {
		width: 75%;
	}

	.row>.off-9-large {
		margin-left: 75%;
	}

	.row>.col-10-large {
		width: 83.33333%;
	}

	.row>.off-10-large {
		margin-left: 83.33333%;
	}

	.row>.col-11-large {
		width: 91.66667%;
	}

	.row>.off-11-large {
		margin-left: 91.66667%;
	}

	.row>.col-12-large {
		width: 100%;
	}

	.row>.off-12-large {
		margin-left: 100%;
	}

	.row.gtr-0 {
		margin-top: 0;
		margin-left: 0em;
	}

	.row.gtr-0>* {
		padding: 0 0 0 0em;
	}

	.row.gtr-0.gtr-uniform {
		margin-top: 0em;
	}

	.row.gtr-0.gtr-uniform>* {
		padding-top: 0em;
	}

	.row.gtr-25 {
		margin-top: 0;
		margin-left: -0.5em;
	}

	.row.gtr-25>* {
		padding: 0 0 0 0.5em;
	}

	.row.gtr-25.gtr-uniform {
		margin-top: -0.5em;
	}

	.row.gtr-25.gtr-uniform>* {
		padding-top: 0.5em;
	}

	.row.gtr-50 {
		margin-top: 0;
		margin-left: -1em;
	}

	.row.gtr-50>* {
		padding: 0 0 0 1em;
	}

	.row.gtr-50.gtr-uniform {
		margin-top: -1em;
	}

	.row.gtr-50.gtr-uniform>* {
		padding-top: 1em;
	}

	.row {
		margin-top: 0;
		margin-left: -2em;
	}

	.row>* {
		padding: 0 0 0 2em;
	}

	.row.gtr-uniform {
		margin-top: -2em;
	}

	.row.gtr-uniform>* {
		padding-top: 2em;
	}

	.row.gtr-150 {
		margin-top: 0;
		margin-left: -3em;
	}

	.row.gtr-150>* {
		padding: 0 0 0 3em;
	}

	.row.gtr-150.gtr-uniform {
		margin-top: -3em;
	}

	.row.gtr-150.gtr-uniform>* {
		padding-top: 3em;
	}

	.row.gtr-200 {
		margin-top: 0;
		margin-left: -4em;
	}

	.row.gtr-200>* {
		padding: 0 0 0 4em;
	}

	.row.gtr-200.gtr-uniform {
		margin-top: -4em;
	}

	.row.gtr-200.gtr-uniform>* {
		padding-top: 4em;
	}

}

@media screen and (max-width: 980px) {

	.row {
		display: flex;
		flex-wrap: wrap;
		box-sizing: border-box;
		align-items: stretch;
	}

	.row>* {
		box-sizing: border-box;
	}

	.row.gtr-uniform>*> :last-child {
		margin-bottom: 0;
	}

	.row.aln-left {
		justify-content: flex-start;
	}

	.row.aln-center {
		justify-content: center;
	}

	.row.aln-right {
		justify-content: flex-end;
	}

	.row.aln-top {
		align-items: flex-start;
	}

	.row.aln-middle {
		align-items: center;
	}

	.row.aln-bottom {
		align-items: flex-end;
	}

	.row>.imp-medium {
		order: -1;
	}

	.row>.col-1-medium {
		width: 8.33333%;
	}

	.row>.off-1-medium {
		margin-left: 8.33333%;
	}

	.row>.col-2-medium {
		width: 16.66667%;
	}

	.row>.off-2-medium {
		margin-left: 16.66667%;
	}

	.row>.col-3-medium {
		width: 25%;
	}

	.row>.off-3-medium {
		margin-left: 25%;
	}

	.row>.col-4-medium {
		width: 33.33333%;
	}

	.row>.off-4-medium {
		margin-left: 33.33333%;
	}

	.row>.col-5-medium {
		width: 41.66667%;
	}

	.row>.off-5-medium {
		margin-left: 41.66667%;
	}

	.row>.col-6-medium {
		width: 50%;
	}

	.row>.off-6-medium {
		margin-left: 50%;
	}

	.row>.col-7-medium {
		width: 58.33333%;
	}

	.row>.off-7-medium {
		margin-left: 58.33333%;
	}

	.row>.col-8-medium {
		width: 66.66667%;
	}

	.row>.off-8-medium {
		margin-left: 66.66667%;
	}

	.row>.col-9-medium {
		width: 75%;
	}

	.row>.off-9-medium {
		margin-left: 75%;
	}

	.row>.col-10-medium {
		width: 83.33333%;
	}

	.row>.off-10-medium {
		margin-left: 83.33333%;
	}

	.row>.col-11-medium {
		width: 91.66667%;
	}

	.row>.off-11-medium {
		margin-left: 91.66667%;
	}

	.row>.col-12-medium {
		width: 100%;
	}

	.row>.off-12-medium {
		margin-left: 100%;
	}

	.row.gtr-0 {
		margin-top: 0;
		margin-left: 0em;
	}

	.row.gtr-0>* {
		padding: 0 0 0 0em;
	}

	.row.gtr-0.gtr-uniform {
		margin-top: 0em;
	}

	.row.gtr-0.gtr-uniform>* {
		padding-top: 0em;
	}

	.row.gtr-25 {
		margin-top: 0;
		margin-left: -0.5em;
	}

	.row.gtr-25>* {
		padding: 0 0 0 0.5em;
	}

	.row.gtr-25.gtr-uniform {
		margin-top: -0.5em;
	}

	.row.gtr-25.gtr-uniform>* {
		padding-top: 0.5em;
	}

	.row.gtr-50 {
		margin-top: 0;
		margin-left: -1em;
	}

	.row.gtr-50>* {
		padding: 0 0 0 1em;
	}

	.row.gtr-50.gtr-uniform {
		margin-top: -1em;
	}

	.row.gtr-50.gtr-uniform>* {
		padding-top: 1em;
	}

	.row {
		margin-top: 0;
		margin-left: -2em;
	}

	.row>* {
		padding: 0 0 0 2em;
	}

	.row.gtr-uniform {
		margin-top: -2em;
	}

	.row.gtr-uniform>* {
		padding-top: 2em;
	}

	.row.gtr-150 {
		margin-top: 0;
		margin-left: -3em;
	}

	.row.gtr-150>* {
		padding: 0 0 0 3em;
	}

	.row.gtr-150.gtr-uniform {
		margin-top: -3em;
	}

	.row.gtr-150.gtr-uniform>* {
		padding-top: 3em;
	}

	.row.gtr-200 {
		margin-top: 0;
		margin-left: -4em;
	}

	.row.gtr-200>* {
		padding: 0 0 0 4em;
	}

	.row.gtr-200.gtr-uniform {
		margin-top: -4em;
	}

	.row.gtr-200.gtr-uniform>* {
		padding-top: 4em;
	}

}

@media screen and (max-width: 736px) {

	.row {
		display: flex;
		flex-wrap: wrap;
		box-sizing: border-box;
		align-items: stretch;
	}

	.row>* {
		box-sizing: border-box;
	}

	.row.gtr-uniform>*> :last-child {
		margin-bottom: 0;
	}

	.row.aln-left {
		justify-content: flex-start;
	}

	.row.aln-center {
		justify-content: center;
	}

	.row.aln-right {
		justify-content: flex-end;
	}

	.row.aln-top {
		align-items: flex-start;
	}

	.row.aln-middle {
		align-items: center;
	}

	.row.aln-bottom {
		align-items: flex-end;
	}

	.row>.imp-small {
		order: -1;
	}

	.row>.col-1-small {
		width: 8.33333%;
	}

	.row>.off-1-small {
		margin-left: 8.33333%;
	}

	.row>.col-2-small {
		width: 16.66667%;
	}

	.row>.off-2-small {
		margin-left: 16.66667%;
	}

	.row>.col-3-small {
		width: 25%;
	}

	.row>.off-3-small {
		margin-left: 25%;
	}

	.row>.col-4-small {
		width: 33.33333%;
	}

	.row>.off-4-small {
		margin-left: 33.33333%;
	}

	.row>.col-5-small {
		width: 41.66667%;
	}

	.row>.off-5-small {
		margin-left: 41.66667%;
	}

	.row>.col-6-small {
		width: 50%;
	}

	.row>.off-6-small {
		margin-left: 50%;
	}

	.row>.col-7-small {
		width: 58.33333%;
	}

	.row>.off-7-small {
		margin-left: 58.33333%;
	}

	.row>.col-8-small {
		width: 66.66667%;
	}

	.row>.off-8-small {
		margin-left: 66.66667%;
	}

	.row>.col-9-small {
		width: 75%;
	}

	.row>.off-9-small {
		margin-left: 75%;
	}

	.row>.col-10-small {
		width: 83.33333%;
	}

	.row>.off-10-small {
		margin-left: 83.33333%;
	}

	.row>.col-11-small {
		width: 91.66667%;
	}

	.row>.off-11-small {
		margin-left: 91.66667%;
	}

	.row>.col-12-small {
		width: 100%;
	}

	.row>.off-12-small {
		margin-left: 100%;
	}

	.row.gtr-0 {
		margin-top: 0;
		margin-left: 0em;
	}

	.row.gtr-0>* {
		padding: 0 0 0 0em;
	}

	.row.gtr-0.gtr-uniform {
		margin-top: 0em;
	}

	.row.gtr-0.gtr-uniform>* {
		padding-top: 0em;
	}

	.row.gtr-25 {
		margin-top: 0;
		margin-left: -0.375em;
	}

	.row.gtr-25>* {
		padding: 0 0 0 0.375em;
	}

	.row.gtr-25.gtr-uniform {
		margin-top: -0.375em;
	}

	.row.gtr-25.gtr-uniform>* {
		padding-top: 0.375em;
	}

	.row.gtr-50 {
		margin-top: 0;
		margin-left: -0.75em;
	}

	.row.gtr-50>* {
		padding: 0 0 0 0.75em;
	}

	.row.gtr-50.gtr-uniform {
		margin-top: -0.75em;
	}

	.row.gtr-50.gtr-uniform>* {
		padding-top: 0.75em;
	}

	.row {
		margin-top: 0;
		margin-left: -1.5em;
	}

	.row>* {
		padding: 0 0 0 1.5em;
	}

	.row.gtr-uniform {
		margin-top: -1.5em;
	}

	.row.gtr-uniform>* {
		padding-top: 1.5em;
	}

	.row.gtr-150 {
		margin-top: 0;
		margin-left: -2.25em;
	}

	.row.gtr-150>* {
		padding: 0 0 0 2.25em;
	}

	.row.gtr-150.gtr-uniform {
		margin-top: -2.25em;
	}

	.row.gtr-150.gtr-uniform>* {
		padding-top: 2.25em;
	}

	.row.gtr-200 {
		margin-top: 0;
		margin-left: -3em;
	}

	.row.gtr-200>* {
		padding: 0 0 0 3em;
	}

	.row.gtr-200.gtr-uniform {
		margin-top: -3em;
	}

	.row.gtr-200.gtr-uniform>* {
		padding-top: 3em;
	}

}

@media screen and (max-width: 480px) {

	.row {
		display: flex;
		flex-wrap: wrap;
		box-sizing: border-box;
		align-items: stretch;
	}

	.row>* {
		box-sizing: border-box;
	}

	.row.gtr-uniform>*> :last-child {
		margin-bottom: 0;
	}

	.row.aln-left {
		justify-content: flex-start;
	}

	.row.aln-center {
		justify-content: center;
	}

	.row.aln-right {
		justify-content: flex-end;
	}

	.row.aln-top {
		align-items: flex-start;
	}

	.row.aln-middle {
		align-items: center;
	}

	.row.aln-bottom {
		align-items: flex-end;
	}

	.row>.imp-xsmall {
		order: -1;
	}

	.row>.col-1-xsmall {
		width: 8.33333%;
	}

	.row>.off-1-xsmall {
		margin-left: 8.33333%;
	}

	.row>.col-2-xsmall {
		width: 16.66667%;
	}

	.row>.off-2-xsmall {
		margin-left: 16.66667%;
	}

	.row>.col-3-xsmall {
		width: 25%;
	}

	.row>.off-3-xsmall {
		margin-left: 25%;
	}

	.row>.col-4-xsmall {
		width: 33.33333%;
	}

	.row>.off-4-xsmall {
		margin-left: 33.33333%;
	}

	.row>.col-5-xsmall {
		width: 41.66667%;
	}

	.row>.off-5-xsmall {
		margin-left: 41.66667%;
	}

	.row>.col-6-xsmall {
		width: 50%;
	}

	.row>.off-6-xsmall {
		margin-left: 50%;
	}

	.row>.col-7-xsmall {
		width: 58.33333%;
	}

	.row>.off-7-xsmall {
		margin-left: 58.33333%;
	}

	.row>.col-8-xsmall {
		width: 66.66667%;
	}

	.row>.off-8-xsmall {
		margin-left: 66.66667%;
	}

	.row>.col-9-xsmall {
		width: 75%;
	}

	.row>.off-9-xsmall {
		margin-left: 75%;
	}

	.row>.col-10-xsmall {
		width: 83.33333%;
	}

	.row>.off-10-xsmall {
		margin-left: 83.33333%;
	}

	.row>.col-11-xsmall {
		width: 91.66667%;
	}

	.row>.off-11-xsmall {
		margin-left: 91.66667%;
	}

	.row>.col-12-xsmall {
		width: 100%;
	}

	.row>.off-12-xsmall {
		margin-left: 100%;
	}

	.row.gtr-0 {
		margin-top: 0;
		margin-left: 0em;
	}

	.row.gtr-0>* {
		padding: 0 0 0 0em;
	}

	.row.gtr-0.gtr-uniform {
		margin-top: 0em;
	}

	.row.gtr-0.gtr-uniform>* {
		padding-top: 0em;
	}

	.row.gtr-25 {
		margin-top: 0;
		margin-left: -0.375em;
	}

	.row.gtr-25>* {
		padding: 0 0 0 0.375em;
	}

	.row.gtr-25.gtr-uniform {
		margin-top: -0.375em;
	}

	.row.gtr-25.gtr-uniform>* {
		padding-top: 0.375em;
	}

	.row.gtr-50 {
		margin-top: 0;
		margin-left: -0.75em;
	}

	.row.gtr-50>* {
		padding: 0 0 0 0.75em;
	}

	.row.gtr-50.gtr-uniform {
		margin-top: -0.75em;
	}

	.row.gtr-50.gtr-uniform>* {
		padding-top: 0.75em;
	}

	.row {
		margin-top: 0;
		margin-left: -1.5em;
	}

	.row>* {
		padding: 0 0 0 1.5em;
	}

	.row.gtr-uniform {
		margin-top: -1.5em;
	}

	.row.gtr-uniform>* {
		padding-top: 1.5em;
	}

	.row.gtr-150 {
		margin-top: 0;
		margin-left: -2.25em;
	}

	.row.gtr-150>* {
		padding: 0 0 0 2.25em;
	}

	.row.gtr-150.gtr-uniform {
		margin-top: -2.25em;
	}

	.row.gtr-150.gtr-uniform>* {
		padding-top: 2.25em;
	}

	.row.gtr-200 {
		margin-top: 0;
		margin-left: -3em;
	}

	.row.gtr-200>* {
		padding: 0 0 0 3em;
	}

	.row.gtr-200.gtr-uniform {
		margin-top: -3em;
	}

	.row.gtr-200.gtr-uniform>* {
		padding-top: 3em;
	}

}

/* Section/Article */

section.special,
article.special {
	text-align: center;
}

header p {
	color: #b2b2b2;
	position: relative;
	margin: 0 0 1.5em 0;
}

header h2+p {
	font-size: 1.25em;
	margin-top: -1em;
	line-height: 1.5em;
}

header h3+p {
	font-size: 1.1em;
	margin-top: -0.8em;
	line-height: 1.5em;
}

header h4+p,
header h5+p,
header h6+p {
	font-size: 0.9em;
	margin-top: -0.6em;
	line-height: 1.5em;
}

header.major h2 {
	font-size: 3em;
	font-weight: bold;
	color: #000;
	margin: 0px;
}

header.major span {
	font-size: 24px;
	color: #5d5c5c;
}

.discription {
	width: calc(100% - 440px);
	padding-top: 10px;
}

/* Form */

form {
	margin: 0 0 2em 0;
}

label {
	color: #787878;
	display: block;
	font-size: 0.9em;
	font-weight: 400;
	margin: 0 0 0em 0;
}

input[type="text"],
input[type="password"],
input[type="email"],
select,
textarea {
	-moz-appearance: none;
	-webkit-appearance: none;
	-ms-appearance: none;
	appearance: none;
	border: none;
	color: inherit;
	display: block;
	outline: 0;
	padding: 0 0em;
	text-decoration: none;
	width: 100%;
	padding: 0 0.75em;
	color: #020202;
}

input[type="text"]:invalid,
input[type="password"]:invalid,
input[type="email"]:invalid,
select:invalid,
textarea:invalid {
	box-shadow: none;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
	border-color: #49bf9d;
}

.forminner .form-control:focus {
	box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

select {
	background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' preserveAspectRatio='none' viewBox='0 0 40 40'%3E%3Cpath d='M9.4,12.3l10.4,10.4l10.4-10.4c0.2-0.2,0.5-0.4,0.9-0.4c0.3,0,0.6,0.1,0.9,0.4l3.3,3.3c0.2,0.2,0.4,0.5,0.4,0.9 c0,0.4-0.1,0.6-0.4,0.9L20.7,31.9c-0.2,0.2-0.5,0.4-0.9,0.4c-0.3,0-0.6-0.1-0.9-0.4L4.3,17.3c-0.2-0.2-0.4-0.5-0.4-0.9 c0-0.4,0.1-0.6,0.4-0.9l3.3-3.3c0.2-0.2,0.5-0.4,0.9-0.4S9.1,12.1,9.4,12.3z' fill='%23dfdfdf' /%3E%3C/svg%3E");
	background-size: 1.25rem;
	background-repeat: no-repeat;
	background-position: calc(100% - 1rem) center;
	height: 2.75em;
	padding-right: 2.75em;
	text-overflow: ellipsis;
}

select option {
	color: #787878;
	background: #fff;
}

select:focus::-ms-value {
	background-color: transparent;
}

select::-ms-expand {
	display: none;
}

input[type="text"],
input[type="password"],
input[type="email"],
select {
	height: 2.75em;
}

textarea {
	padding: 0.75em;
}

input[type="checkbox"],
input[type="radio"] {
	-moz-appearance: none;
	-webkit-appearance: none;
	-ms-appearance: none;
	appearance: none;
	display: block;
	float: left;
	margin-right: -2em;
	opacity: 0;
	width: 1em;
	z-index: -1;
}

input[type="checkbox"]+label,
input[type="radio"]+label {
	text-decoration: none;
	color: #a2a2a2;
	cursor: pointer;
	display: inline-block;
	font-size: 1em;
	font-weight: 400;
	padding-left: 2.4em;
	padding-right: 0.75em;
	position: relative;
}

input[type="checkbox"]+label:before,
input[type="radio"]+label:before {
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	display: inline-block;
	font-style: normal;
	font-variant: normal;
	text-rendering: auto;
	line-height: 1;
	text-transform: none !important;
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
}

input[type="checkbox"]+label:before,
input[type="radio"]+label:before {
	background: #f7f7f7;
	border-radius: 0.35em;
	border: solid 2px transparent;
	content: '';
	display: inline-block;
	font-size: 0.8em;
	height: 2.0625em;
	left: 0;
	line-height: 1.85625em;
	position: absolute;
	text-align: center;
	top: 0;
	width: 2.0625em;
}

input[type="checkbox"]:checked+label:before,
input[type="radio"]:checked+label:before {
	background: #787878;
	border-color: #787878;
	color: #fff;
	content: '\f00c';
}

input[type="checkbox"]:focus+label:before,
input[type="radio"]:focus+label:before {
	border-color: #49bf9d;
}

input[type="checkbox"]+label:before {
	border-radius: 0.35em;
}

input[type="radio"]+label:before {
	border-radius: 100%;
}

::-webkit-input-placeholder {
	color: #b2b2b2 !important;
	opacity: 1.0;
}

:-moz-placeholder {
	color: #b2b2b2 !important;
	opacity: 1.0;
}

::-moz-placeholder {
	color: #b2b2b2 !important;
	opacity: 1.0;
}

:-ms-input-placeholder {
	color: #b2b2b2 !important;
	opacity: 1.0;
}

/* Box */

.box {
	border-radius: 0.35em;
	border: solid 2px #efefef;
	margin-bottom: 2em;
	padding: 1.5em;
}

.box> :last-child,
.box> :last-child> :last-child,
.box> :last-child> :last-child> :last-child {
	margin-bottom: 0;
}

.box.alt {
	border: 0;
	border-radius: 0;
	padding: 0;
}

/* Icon */

.icon {
	text-decoration: none;
	border-bottom: none;
	position: relative;
}

.icon:before {
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	display: inline-block;
	font-style: normal;
	font-variant: normal;
	text-rendering: auto;
	line-height: 1;
	text-transform: none !important;
	font-family: 'Font Awesome 5 Free';
	font-weight: 400;
}

.icon>.label {
	display: none;
}

.icon:before {
	line-height: inherit;
}

.icon.solid:before {
	font-weight: 900;
}

.icon.brands:before {
	font-family: 'Font Awesome 5 Brands';
}



.image img {
	border-radius: 0.35em;
	display: block;
}

.image.left {
	float: left;
	margin: 0 1.5em 1em 0;
	top: 0.25em;
}

.image.right {
	float: right;
	margin: 0 0 1em 1.5em;
	top: 0.25em;
}

.image.left,
.image.right {
	max-width: 40%;
}

.image.left img,
.image.right img {
	width: 100%;
}

.image.fit {
	display: block;

	width: 100%;
}

.image.fit img {
	width: 100%;
}

.image.avatar {
	border-radius: 100%;
}

.image.avatar:before {
	display: none;
}

.image.avatar img {
	border-radius: 100%;
	width: 100%;
}

.addBanner {
	position: relative;
}

.addBanner .image img {
	border-radius: 0px;
}

.discount-pan {
	position: absolute;
	top: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.8);
	color: #fff;
	width: 500px;
	height: 100%;
	padding: 90px 40px 40px 40px;
}

.discount-pan h3 {
	font-size: 50px;
	font-weight: bold;
	color: #ffd400;
	margin: 0px;
	padding-bottom: 20px;
}

.discount-pan span {
	font-size: 25px;
}

.discount-pan p {
	font-size: 14px;
	font-weight: normal;
}

.readmore {
	background-color: #ffa100;
	padding: 10px 40px;
	color: #ffffff;
	border: none;
	border-radius: 50px;
	font-weight: bold;
}

.readmore:hover {
	background: #000;
}

/* List */

ol {
	list-style: decimal;
	margin: 0 0 2em 0;
	padding-left: 1.25em;
}

ol li {
	padding-left: 0.25em;
}

ul {
	list-style: disc;
	margin: 0 0 2em 0;
	padding-left: 1em;
}

ul li {
	padding-left: 0.5em;
}

ul.alt {
	list-style: none;
	padding-left: 0;
}

ul.alt li {
	border-top: solid 2px #efefef;
	padding: 0.5em 0;
}

ul.alt li:first-child {
	border-top: 0;
	padding-top: 0;
}

dl {
	margin: 0 0 2em 0;
}

/* Icons */

ul.icons {
	cursor: default;
	list-style: none;
	padding-left: 0;
}

ul.icons li {
	display: inline-block;
	padding: 0 1em 0 0;
}

ul.icons li:last-child {
	padding-right: 0;
}

ul.icons li .icon:before {
	font-size: 1.5em;
}

/* Labeled Icons */

ul.labeled-icons {
	list-style: none;
	padding: 0;
}

ul.labeled-icons li {
	line-height: 1.75em;
	margin: 1.5em 0 0 0;
	padding-left: 2.25em;
	position: relative;
}

ul.labeled-icons li:first-child {
	margin-top: 0;
}

ul.labeled-icons li a {
	color: inherit;
}

ul.labeled-icons li h3 {
	color: #b2b2b2;
	left: 0;
	position: absolute;
	text-align: center;
	top: 0;
	width: 1em;
}

/* Actions */

ul.actions {
	display: -moz-flex;
	display: -webkit-flex;
	display: -ms-flex;
	display: flex;
	cursor: default;
	list-style: none;
	margin-left: -1em;
	padding-left: 0;
}

ul.actions li {
	padding: 0 0 0 1em;
	vertical-align: middle;
}

ul.actions.special {
	-moz-justify-content: center;
	-webkit-justify-content: center;
	-ms-justify-content: center;
	justify-content: center;
	width: 100%;
	margin-left: 0;
}

ul.actions.special li:first-child {
	padding-left: 0;
}

ul.actions.stacked {
	-moz-flex-direction: column;
	-webkit-flex-direction: column;
	-ms-flex-direction: column;
	flex-direction: column;
	margin-left: 0;
}

ul.actions.stacked li {
	padding: 1.3em 0 0 0;
}

ul.actions.stacked li:first-child {
	padding-top: 0;
}

ul.actions.fit {
	width: calc(100% + 1em);
}

ul.actions.fit li {
	-moz-flex-grow: 1;
	-webkit-flex-grow: 1;
	-ms-flex-grow: 1;
	flex-grow: 1;
	-moz-flex-shrink: 1;
	-webkit-flex-shrink: 1;
	-ms-flex-shrink: 1;
	flex-shrink: 1;
	width: 100%;
}

ul.actions.fit li>* {
	width: 100%;
}

ul.actions.fit.stacked {
	width: 100%;
}

@media screen and (max-width: 480px) {

	ul.actions:not(.fixed) {
		-moz-flex-direction: column;
		-webkit-flex-direction: column;
		-ms-flex-direction: column;
		flex-direction: column;
		margin-left: 0;
		width: 100% !important;
	}

	ul.actions:not(.fixed) li {
		-moz-flex-grow: 1;
		-webkit-flex-grow: 1;
		-ms-flex-grow: 1;
		flex-grow: 1;
		-moz-flex-shrink: 1;
		-webkit-flex-shrink: 1;
		-ms-flex-shrink: 1;
		flex-shrink: 1;
		padding: 1em 0 0 0;
		text-align: center;
		width: 100%;
	}

	ul.actions:not(.fixed) li>* {
		width: 100%;
	}

	ul.actions:not(.fixed) li:first-child {
		padding-top: 0;
	}

	ul.actions:not(.fixed) li input[type="submit"],
	ul.actions:not(.fixed) li input[type="reset"],
	ul.actions:not(.fixed) li input[type="button"],
	ul.actions:not(.fixed) li button,
	ul.actions:not(.fixed) li .button {
		width: 100%;
	}

	ul.actions:not(.fixed) li input[type="submit"].icon:before,
	ul.actions:not(.fixed) li input[type="reset"].icon:before,
	ul.actions:not(.fixed) li input[type="button"].icon:before,
	ul.actions:not(.fixed) li button.icon:before,
	ul.actions:not(.fixed) li .button.icon:before {
		margin-left: -0.5em;
	}

}

/*---- Side Nav ----- */

/* .toggle{
			position: absolute;
left: 14px;
top: 10px;
font-size:20px;
cursor:pointer;
color:black;
		}

		.sidenav {
			height: 100%;
			width: 0;
			position: fixed;
			z-index: 1;
			top: 0;
			left: 0;
			background-color: #111;
			overflow-x: hidden;
			transition: 0.5s;
			padding-top: 60px;
		  }
		  
		  .sidenav a {
			padding: 8px 8px 8px 32px;
			text-decoration: none;
			font-size: 16px;
			color: #818181;
			display: block;
			transition: 0.3s;
		  }
		  
		  .sidenav a:hover {
			color: #f1f1f1;
		  }
		  
		  .sidenav .closebtn {
			position: absolute;
			top: 0;
			right: 25px;
			font-size: 36px;
			margin-left: 50px;
		  }
		  
		  
		  @media screen and (max-height: 450px) {
			.sidenav {padding-top: 15px;}
			.sidenav a {font-size: 18px;}
		  }

----- End of  Side Nam ------ */

/* Table */

.table-wrapper {
	-webkit-overflow-scrolling: touch;
	overflow-x: auto;
}

table {
	margin: 0 0 2em 0;
	width: 100%;
}

table tbody tr {
	border: solid 1px #efefef;
	border-left: 0;
	border-right: 0;
}

table tbody tr:nth-child(2n + 1) {
	background-color: #f7f7f7;
}

table td {
	padding: 0.75em 0.75em;
}

table th {
	color: #787878;
	font-size: 0.9em;
	font-weight: 400;
	padding: 0 0.75em 0.75em 0.75em;
	text-align: left;
}

table thead {
	border-bottom: solid 2px #efefef;
}

table tfoot {
	border-top: solid 2px #efefef;
}

table.alt {
	border-collapse: separate;
}

table.alt tbody tr td {
	border: solid 2px #efefef;
	border-left-width: 0;
	border-top-width: 0;
}

table.alt tbody tr td:first-child {
	border-left-width: 2px;
}

table.alt tbody tr:first-child td {
	border-top-width: 2px;
}

table.alt thead {
	border-bottom: 0;
}

table.alt tfoot {
	border-top: 0;
}

/* Button */
.button-circle {
	padding: 10px;
	border: 1px solid #c4c4c4;
	border-radius: 30px;
	width: 40px;
	height: 40px;
	display: inline-block;
	line-height: 18px;
	text-align: center;
	margin-right: 10px;
	cursor: pointer;
}

input[type="submit"],
input[type="reset"],
input[type="button"],
.button {
	-moz-appearance: none;
	-webkit-appearance: none;
	-ms-appearance: none;
	appearance: none;
	-moz-transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border-color 0.2s ease-in-out;
	-webkit-transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border-color 0.2s ease-in-out;
	-ms-transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border-color 0.2s ease-in-out;
	transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border-color 0.2s ease-in-out;
	background-color: transparent;
	border-radius: 0.35em;
	border: solid 3px #efefef;
	color: #787878 !important;
	cursor: pointer;
	display: inline-block;
	font-weight: 400;
	height: 3.15em;
	height: calc(2.75em + 6px);
	line-height: 2.75em;
	min-width: 10em;
	padding: 0 1.5em;
	text-align: center;
	text-decoration: none;
	white-space: nowrap;
}

input[type="submit"]:hover,
input[type="reset"]:hover,
input[type="button"]:hover,
.button:hover {
	border-color: #49bf9d;
	color: #49bf9d !important;
}

input[type="submit"]:active,
input[type="reset"]:active,
input[type="button"]:active,
.button:active {
	background-color: rgba(73, 191, 157, 0.1);
	border-color: #49bf9d;
	color: #49bf9d !important;
}

input[type="submit"].icon,
input[type="reset"].icon,
input[type="button"].icon,
.button.icon {
	padding-left: 1.35em;
}

input[type="submit"].icon:before,
input[type="reset"].icon:before,
input[type="button"].icon:before,
.button.icon:before {
	margin-right: 0.5em;
}

input[type="submit"].fit,
input[type="reset"].fit,
input[type="button"].fit,
.button.fit {
	min-width: 0;
	width: 100%;
}

input[type="submit"].small,
input[type="reset"].small,
input[type="button"].small,
.button.small {
	font-size: 0.8em;
}

input[type="submit"].large,
input[type="reset"].large,
input[type="button"].large,
.button.large {
	font-size: 1.35em;
}

input[type="submit"].primary,
input[type="reset"].primary,
input[type="button"].primary,
.button.primary {
	background-color: #49bf9d;
	border-color: #49bf9d;
	color: #ffffff !important;
}

input[type="submit"].primary:hover,
input[type="reset"].primary:hover,
input[type="button"].primary:hover,
.button.primary:hover {
	background-color: #5cc6a7;
	border-color: #5cc6a7;
}

input[type="submit"].primary:active,
input[type="reset"].primary:active,
input[type="button"].primary:active,
.button.primary:active {
	background-color: #3eb08f;
	border-color: #3eb08f;
}

input[type="submit"].disabled,
input[type="submit"]:disabled,
input[type="reset"].disabled,
input[type="reset"]:disabled,
input[type="button"].disabled,
input[type="button"]:disabled,
.button.disabled,
.button:disabled {
	background-color: #e7e7e7 !important;
	border-color: #e7e7e7 !important;
	color: #b2b2b2 !important;
	cursor: default;
}


/* Work Item */
.topmenu .navbar {
	padding: 0px;
}

#collapsibleNavbar {
	position: absolute;
	right: 0px;
	top: 0px;
}


.work-item {
	margin: 0 0 1em 0;
}

.work-item .image {
	margin: 0 0 1.5em 0;
	box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.2);
	border-radius: 5px;
	padding-bottom: 15px;
	min-height: 270px;
}

.work-item h3 {
	font-size: 1.2em;
	margin: 0 0 0 0;
	color: #4a4a4a;
	padding: 0 0.3em 0 1em;
	font-weight: bold;
}

.work-item p {
	font-size: 0.9em;
	line-height: 1.4em;
	margin: 0;
	color: #4a4a4a;
	padding: 0 0.5em 0 1.3em;
}

/* Header */

#header {
	background-color: #ffffff;
	height: 100%;
	left: 0;
	padding: 0px;
	padding-bottom: 4em;
	position: fixed;
	top: 0;
	width: 30%;
	overflow-y: scroll;
	overflow: auto;
	border-right: 1px solid #f5f5f5;
	box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}

#header.login-header {
	min-height: 100vh;
}

#header.registration-header {
	min-height: 100vh;
}

.logincontent {
	background-image: url(../images/login.png);
	background-repeat: no-repeat;
	background-position: 100% 0%, bottom right;
	height: 100%;
}

.loginpage {
	padding: 3em 15em 0em 0em;
	background-image: url(../images/log-top-bg.png), url(../images/log-bottom-bg.png);
	background-repeat: no-repeat;
	background-position: 100% 0%, bottom right;
	height: 100vh;
}

.loginpage h2 {
	color: #201e1e;
	font-size: 2em;
	margin: 0px;
	width: calc(100% - 500px);
	line-height: 35px;
	font-weight: bold;
	margin-bottom: 10px;
}

.loginpage p {
	width: calc(100% - 380px);
}

.loginpage label.switch {
	position: relative;
	display: inline-block;
	width: 61px;
	height: 33px;
	text-align: center;
	line-height: 32px;
	color: white;
	font-size: 13px;
}

.loginheader {
	min-height: auto;
}

.loginform {
	background: #fff;
	padding: 10px 0px;
	margin: -30px 15px 0em 15px;
	padding: 1em;
	border-radius: 5px;
	box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.1);
	/* background-image: url(images/log-top-bg.png), url(images/log-bottom-bg.png); */
	background-repeat: no-repeat;
	background-position: 100% 0%, bottom right;
	background-size: 20%, 30%;
}


.topheader {
	background: rgb(248, 253, 194);
	background: linear-gradient(to bottom, rgb(255 255 255) 1%, rgb(255 255 255) 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f8fdc2', endColorstr='#fbf675', GradientType=0);
	text-align: center;
	padding: 2em 3em;
	min-height: 243px;
	margin-top: 20px;
	margin-bottom: 28px;
}

.topheader.loginheader {
	min-height: auto;
}

.logo {
	margin-bottom: 10px;
	display: inline-block;

}

.bookType {
	background: #000000;
	border-radius: 10px;
	border: none;
	display: flex;
	padding: 2px;
}

.bookType a {
	padding: 0.4em;
	border: none;
	width: 100%;
	color: #fff;
	font-weight: bold;

}

.bookType a.active {
	background: #fc0;
	color: #201e1e !important;
	position: relative;
	right: 0;
	border-radius: 8px;
	font-weight: 500;
}

#header>.forminner,
#header>.profile-pan {
	background: #fff;
	padding: 1em;
	margin: -80px 15px 1em 15px;
	padding: 1em;
	width: calc(100% - 30px);
	border-radius: 5px;
	box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}

.hoverable:hover {
	cursor: pointer;
}

.pull-right {
	float: right;
}

#header>.profile-pan {
	margin: -30px 15px 1em 15px;
}

.user-image {
	margin: auto;
	text-align: center;
	width: 100px;
	height: 100px;
	overflow: hidden;
	border-radius: 100px;
	display: block;
}

.user-image img {
	position: relative;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center left;
	height: 100%;
	width: 100%;
	margin-left: auto;
	margin-right: auto;
}

.name {
	text-align: center;
	font-size: 20px;
	color: black;
	padding: 15px;
}

.user-rides h2 {
	color: #201e1e;
	font-size: 2em;
	margin: 0px;
	line-height: 35px;
	font-weight: bold;
	margin-bottom: 40px;
}

.user-rides {
	padding: 6em 4em 0em 0em;
	background-repeat: no-repeat;
	background-position: right 20px;
	background-size: 37%, 100%;
}

.user-rides {
	padding: 6em 4em 0em 0em;
}

.travel-detail,
.booking-detail {
	background-color: #ffbb49;
	padding: 10px 20px 10px 30px;
	position: relative;
}

.pricing {
	font-size: 16px;
	font-weight: bold;
}

.pricing,
.timing,
.vehicle-type {
	border-bottom: 1px dashed #ebebeb;
	padding: 5px 0px;
}

.booking-detail {
	background-color: #fff;
	position: relative;
	color: #000;
	font-size: 14px;
	padding-left: 20px;
	width: 100%;
}

.booking-detail i {
	padding-right: 5px;
}

.pending h3,
.upcoming h3,
.completed h3,
.canceled h3 {
	font-size: 18px;
	font-weight: bold;
}

.upcoming h3 {
	color: #ffa000;
}

.completed h3 {
	color: #08ce08;
}

.pending h3,
.canceled h3 {
	color: #ff4b4b;
}

.pending,
.upcoming,
.completed,
.canceled {

	margin-bottom: 20px;
}

.upcoming .travel-detail {
	background-color: #ffbb49;
}

.completed .travel-detail {
	background-color: #49af49;
}

.canceled .travel-detail,
.pending .travel-detail {
	background-color: #ff4b4b;
}

.city {
	position: relative;
	color: #fff;
	font-size: 14px;
	padding: 5px 0px;
}

.city:before {
	height: 100%;
	content: "";
	width: 1px;
	position: absolute;
	top: 17px;
	left: -16px;
	border: 1px dashed #fff;
}

.completed .city:first-child:after {
	background-color: #49af49;
	border: 1px solid #fff;
}

.canceled .city:first-child:after,
.pending .city:first-child:after {
	background-color: #ff4b4b;
	border: 1px solid #fff;
}

.upcoming .city:first-child:after {
	background-color: #ffbb49;
	border: 1px solid #fff;
}

.city:last-child:before {
	height: auto;
}

.city:after {
	height: 8px;
	content: "";
	width: 8px;
	border-radius: 30px;
	background-color: #fff;
	position: absolute;
	top: 17px;
	left: -19px;
}

.city:first-child:after {
	background-color: gray;
	border: 1px solid #fff;
}

.total-count {
	display: flex;
	color: #000;
	margin-bottom: 20px;
	border-bottom: 1px dashed #ececec;
	padding-bottom: 10px;
}

.total-count span {
	display: block;
	width: 100%;
}

.total-count span:last-child {
	text-align: right;
	color: blue;
	font-size: 12px;
	cursor: pointer;
}

.travel-detail {
	background-color: gray;
}

.request {
	display: flex;
	margin-bottom: 20px;
	background: white;
	box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1);
}

.mapfield .request {
	width: 100%;
	box-shadow: none;
	border: 1px solid #ccc;
	margin: 0px;
}

.head {
	position: relative;
	padding-left: 30px;
	color: rgba(0, 0, 0, 0.7);
	padding-bottom: 10px;
}

.head span:before {
	position: absolute;
	width: 10px;
	height: 10px;
	display: inline-block;
	border-radius: 50px;
	border: 9px solid rgb(255, 204, 0);
	content: '';
	left: 0;
	top: 5px;
	background: rgb(255, 204, 0);
}

.head span:after {
	position: absolute;
	width: 10px;
	height: 10px;
	display: inline-block;
	border-radius: 50px;
	content: '';
	left: 4px;
	top: 9px;
	background: rgba(255, 167, 0, 0.74);
}

#header strong,
#header b {
	color: #ffffff;
}

#header h2,
#header h3,
#header h4,
#header h5,
#header h6 {
	color: #ffffff;
}

#header h1 {
	color: rgba(255, 255, 255, 0.5);
	font-size: 1.35em;
	line-height: 1.75em;
	margin: 0;
}

#header .image.avatar {
	margin: 0 0 1em 0;
	width: 6.25em;
}

.forminner form {
	margin: 0px;
}

.forminner .form-group {
	height: 3.75em;
}

.forminner .form-group label {
	color: #787878;
	display: block;
	font-size: 0.9em;
	font-weight: 400;
	margin: 0 0 0em 0;
	text-align: left;
}

.forminner .form-group input[type="text"],
.forminner .form-group input[type="password"],
.forminner .form-group input[type="email"],
.forminner .form-group select,
.forminner .form-group textarea {
	height: 2em;
	font-size: 18px;
	color: #675d5d;
	background-color: #ffffff;
	padding: 0px;
}

.line {
	width: 100%;
	height: 1px;
	background-color: gainsboro;
	margin: 5px auto;
	position: relative;
}

.line a {
	position: absolute;
	top: -20px;
	right: 0px;

}

.next-btn {
	text-align: right;
	padding: 0px 20px;
	display: flow;
}

.next-btn a {
	background: #ffcc00;
	padding: 8px 20px;
	border-radius: 10px;
	color: #fff;
	font-size: 14px;
	float: right;
}

.next-btn a:hover {
	background: #000;
}

/*---- Pic/DropOff----*/
.smallheader .topheader {
	min-height: 80px;
	padding: 1em;
}

#header.smallheader .forminner {
	margin-top: -20px;
}

.head a {
	position: relative;
	top: 2px;
	right: -10px;
	color: #000;
}

.smallheader .forminner .form-group,
.add-information .forminner .form-group {
	height: auto;
}

.smallheader .forminner .form-group i,
.add-information .forminner .form-group i {
	font-size: 12px;
	position: relative;
	top: -1px;
	right: 20px;
	color: #000;
}

.smallheader .forminner .form-group input[type="email"],
.smallheader .forminner .form-group input[type="date"],
.smallheader .forminner .form-group input[type="time"],
.add-information .forminner .form-group span,
.address .form-group span {
	font-size: 14px;
	width: calc(100% - 30px);
	display: inline-block;
	margin-left: 12px;
	border: none;
	border-bottom: 1px solid #efefef;
	border-radius: 0px;
	padding: 0px;

}


.mappage {
	height: 100vh;
}

.toggle a {
	color: #585858;
}


/*---- End Pic/DropOff----*/


/*---- Payment Review ----*/

.payment-option {
	display: flex;
	justify-content: space-between;
}

.payment-box {
	display: flex;
	background: #ffffff;
	width: 48%;
	cursor: pointer;
	border-radius: 4px;
	display: block;
	position: relative;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.payment-label {
	display: flex;
	cursor: pointer;
	border: 1px solid #f3f3f3;
	border-radius: 4px;
}

.payment-check {
	position: relative;
	align-self: center;
	margin-left: calc(36.1px /3);
}

.custom-checkbox {
	display: block;
	position: relative;
	padding-left: 35px;
	margin-bottom: 22px;
	cursor: pointer;
	font-size: 22px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

/* Hide the browser's default checkbox */
.custom-checkbox input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
	height: 0;
	width: 0;
}

/* Create a custom checkbox */
.checkmark {
	position: absolute;
	top: 0;
	left: 0;
	height: 20px;
	width: 20px;
	background-color: #f6f7e7;
	border-radius: 3px;
}

/* On mouse-over, add a grey background color */
.custom-checkbox:hover input~.checkmark {
	background-color: #ccc;
}

/* When the checkbox is checked, add a blue background */
.custom-checkbox input:checked~.checkmark {
	background-color: #ffb100;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
	content: "";
	position: absolute;
	display: none;
}

/* Show the checkmark when checked */
.custom-checkbox input:checked~.checkmark:after {
	display: block;
}

/* Style the checkmark/indicator */
.custom-checkbox .checkmark:after {
	left: 8px;
	top: 4px;
	width: 5px;
	height: 10px;
	border: solid white;
	border-width: 0 3px 3px 0;
	-webkit-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
}


.payment-rate {
	display: flex;
	flex-flow: column;
	overflow: hidden;
}

.token-amount {
	margin: 0;
	letter-spacing: calc(1.6px / 3);
	color: #ffb100;
	padding-top: 3px;
	font-weight: bold;
}

.driver-pay {
	font-size: 12px;
	position: relative;
	top: -6px;
	color: #c4c4c4;
}

.fare-heading {
	display: flex;
	border-bottom: 1px solid #f3f3f3;
	margin: 5px 0px;
	padding: 5px 0px;
}

.fare-heading span {
	width: 48%;
	font-size: 14px;
}

.t-right {
	text-align: right;
}

.amount-payable {
	display: flex;
}

.amount-payable span {
	width: 48%;
	font-weight: bold;
	color: #696969;
}

.driver-payable {
	display: flex;
}

.driver-payable span {
	width: 48%;
	font-size: 12px;
	color: #ec4747;
	font-weight: bold;
}

.book-now {
	width: calc(100% - 30px);
	border-radius: 35px;
	box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
	background: #ffa000;
	padding: 0.5em;
	margin: -10px 15px 2em 15px;
	text-align: center;
	color: #fff;
	cursor: pointer;
}

.booking-review {
	padding: 0 1em;
}

.booking-review .cabList,
.add-information {
	margin: 0px;
}

.booking-invoice,
.booking-fare-table,
.address {
	margin: 2% 4%;
	box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
	background: #fff;
}

.add-information .forminner {
	padding: 10px;
	background: #fff;
}

.add-information .form-group {
	position: relative;
}

.single-form {
	margin-bottom: 0px;
}

.add-information .forminner .single-form span {
	border-bottom: 0px;
}

.booking-fare-table {
	padding: 20px;
}

.trip-detail {
	text-align: center;
}

.trip-detail span {
	font-size: 12px;
	font-weight: bold;
	padding-bottom: 5px;
	display: block;
}

.big-heading {
	font-size: 30px;
	padding-top: 10px;
	font-weight: bold;
	border-top: 1px solid #efefef;
	color: #ffb100;
	margin-bottom: 30px;
}

.fare-row,
.total-fare {
	padding: 10px 0px;
	display: flex;
}

.left-col {
	width: 75%;
}

.right-col {
	width: 25%;
}

.left-col,
.right-col {
	font-size: 12px;
	line-height: 16px;
}

.b-head {
	color: #000;
}

.total-fare {
	border-top: 1px solid #efefef;
	border-bottom: 1px solid #efefef;
	margin-top: 20px;

}

.total-fare span {
	font-size: 14px;
}

header.major h2.booking-heading {
	font-size: 2em;
}

.terms-condition h2 {
	font-size: 12px;
}

.terms-condition {
	font-size: 11px;
	font-weight: normal;
	margin: 2em 1em;
	line-height: 18px;
	background: #f9f9f9;
	padding: 1em;
}

/* .add-information .form-group:before {
					height: 46px;
					width: 1px;
					position: absolute;
					content: '';
					background: #bdbdbd;
					top: 16px;
				}
		
				.add-information .form-group:last-child:before {
					height: 4px;
				}
		
				.add-information .form-group:after {
					width: 10px;
					height: 10px;
					position: absolute;
					content: '';
					background: #bdbdbd;
					left: -4px;
					top: 10px;
					border-radius: 40px;
				} */
/*---- End Payment Review ----*/

.cabList,
.driver-info {
	margin: 40px 15px 1em 15px;
	width: calc(100% - 30px);
	overflow: hidden;
}

.info-box {
	background: #fff;
	border-radius: 5px;
	box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1);
	margin-bottom: 15px;
	overflow: hidden;
}

.w3-button {
	background: no-repeat;
	border: none;
	padding: 10px 20px;
	width: 100%;
	text-align: left;
}

.w3-hide {
	display: none;
}

.w3-show-block,
.w3-show {
	display: block !important;
}

.w3-ul {
	list-style: none;
	padding-left: 0.8em;
	padding-top: 0.8em;
	font-size: 14px;
	margin: 0px 0px 5px 0px;
}

.w3-ul li {
	padding-bottom: 1em;
	text-align: left;
}

.w3-ul li i {
	display: inline-block;
	width: 17px;
	text-align: center;
	margin-right: 10px;
}

.w3-button span {
	display: inline-block;

	/* Don't remove float */
	float: right;
	padding: 3px 0;
}

.w3-button {
	background: no-repeat;
	border: none;
	padding: 10px 20px;
	width: 100%;
	text-align: left;
}

.w3-ul li input[type="checkbox"] {
	-moz-appearance: checkbox;
	-webkit-appearance: checkbox;
	-ms-appearance: checkbox;
	appearance: checkbox;
	display: inline-block;
	margin: 0px;
	opacity: inherit;
	float: none;
	margin-right: 10px;
	width: 17px;
}

.cabList ul {
	list-style: none;
	padding: 0px;
	margin: 0px;
}

.cabList ul li {
	background: #fff;
	display: flex;
	min-height: 80px;
	border-radius: 5px;
	box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1);
	margin-bottom: 15px;
	overflow: hidden;
}

.cabList ul li:last-child {
	margin-bottom: 0px;
}

.cabType {
	width: 35%;
	background-image: url(./images/cab-bg.png);
	background-position: top left;
	background-repeat: no-repeat;
	bottom: 0px;
	left: -60px;
	padding: 0em 0em 0px 1em;
	position: relative;
	text-align: center;
}

.cabType img {
	position: absolute;
	left: 70px;
	top: 30px;
}

.cabInfo {
	width: 70%;
	display: flex;
}

.modalType {
	padding: 1em 0px;
	width: 100%;
	margin-left: -12px;
	color: #675d5d;
	text-align: left;
}

.modalType span {
	font-weight: bold;
	color: #2b1b1b;
	font-size: 12px;
}

.modalType small {
	font-size: 11px;
}

.price {
	font-size: 14px;
	margin: 0px;
	padding: 0px;
	line-height: 14px;
}

.bookingDetail {
	padding: 1em 0px;
}

.bookingDetail span {
	width: 47%;
	display: inline-block;
	font-size: 16px;
	text-align: center;
	color: #2b1b1b;
}

.bookingDetail span i {
	font-size: 14px;
}

.bookingDetail span a {
	cursor: pointer;
	color: #2b1b1b;
}

.booking {
	background: #ffa000;
	padding: 0px 18px;
	border-radius: 30px;
	margin-right: 12px;
	font-size: 14px;
	line-height: 28px;
	color: white;
	cursor: pointer;
	margin-top: 10px;
}

.booking:hover {
	background-color: #000;
}


.cards {
	margin: 0 0 1em 0;
}

.cards .image {
	margin: 0 0 1.5em 0;
	box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.2);
	border-radius: 5px;
	padding-bottom: 15px;
	text-align: center;
	min-height: 325px;
}

.cards h3 {
	font-size: 1.2em;
	margin: 0 0 0 0;
	color: #4a4a4a;
	padding: 0 0.3em 0 0.8em;
	font-weight: bold;
}

.cards p {
	font-size: 0.9em;
	line-height: 1.4em;
	margin: 0;
	color: #4a4a4a;
	padding: 0 0.5em 0 1.3em;
}

.cards .image.fit .roundShap {
	border-radius: 0px 0px 120px 120px;
	overflow: hidden;
	display: block;
}

.cards .readmore {
	bottom: 0px;
}

.prices {
	color: #000;
	font-weight: bold;
	padding-top: 5px;
}

.booknow {
	background: #ffa000;
	padding: 5px 28px;
	border-radius: 30px;
	margin-right: 12px;
	font-size: 14px;
	line-height: 28px;
	color: white;
	cursor: pointer;
	margin-top: 40px;
	display: inline-block;
	margin-top: 10px;
}

.booknow:hover {
	background: #000;
}

#four header.major h2 {
	font-size: 2em;
}

#four header.major span {
	font-size: 18px;
}

#four header.major {
	margin-bottom: 20px;
}

.becomeDriver {
    background-color: #faf4d4;
    padding: 6em 1em 6em 2em;
    background-image: url(./images/become-driver2.jpg);
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
}

.becomeDriver small {
	font-size: 1em;
	color: #5d5c5c;
	margin: 0px;
}

.becomeDriver h2 {
	color: #201e1e;
	font-size: 2em;
	margin: 0px;
	width: calc(100% - 500px);
	line-height: 35px;
	font-weight: bold;
	margin-bottom: 10px;
}

.becomeDriver p {
	width: calc(100% - 500px);
	color: #4a4a4a;
	line-height: 20px;
	font-size: 1em;
}

.amenities {
	width: calc(100% - 400px);
}

.amenities span {
	color: #4a4a4a;
	border: 1px solid #4a4a4a;
	padding: 3px 15px;
	border-radius: 30px;
	margin-right: 10px;
	margin-bottom: 10px;
	display: inline-block;
}

.amenities span:hover {
	background: #4a4a4a;
	color: white;
}

/* Become a Driver page */
.driverform {
	padding: 6em 4em 0em 0em;
	background-image: url(./images/become-driver.png), url(./images/become-driver-bg.png);
	background-repeat: no-repeat;
	background-position: 95% 2%, bottom right;
	background-size: 37%, 100%;
}

/* Booking page */
.bookingform.driverform {
	background-image: url(../images/booking.png), url(./images/become-driver-bg.png);
}


.contactform {
	padding: 6em 4em 0em 0em;
	background-image: url(../images/contact.png);
	background-repeat: no-repeat;
	background-position: 90% 1%, bottom right;
	background-size: 37%, 100%;
}


.whitebox {
	padding: 32px;
	padding-top: 45px;
	padding-left: 32px;
	box-shadow: 21px 0px 24px rgba(0, 0, 0, 0.05);
	margin-top: 20px !important;
	padding-bottom: 0px;
}

.driverform h2 {
	color: #201e1e;
	font-size: 2em;
	margin: 0px;
	width: calc(100% - 500px);
	line-height: 35px;
	font-weight: bold;
	margin-bottom: 10px;
}

.driverform label.switch {
	position: relative;
	display: inline-block;
	width: 61px;
	height: 33px;
	text-align: center;
	line-height: 32px;
	color: white;
	font-size: 13px;
}

.switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

.slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: .4s;
	transition: .4s;
}

.slider:before {
	position: absolute;
	content: "";
	height: 26px;
	width: 26px;
	left: 4px;
	bottom: 4px;
	background-color: white;
	-webkit-transition: .4s;
	transition: .4s;
}

input:checked+.slider {
	background-color: #ffcc00;
}

input:focus+.slider {
	box-shadow: 0 0 1px #ffcc00;
}

input:checked+.slider:before {
	-webkit-transform: translateX(26px);
	-ms-transform: translateX(26px);
	transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
	border-radius: 34px;
}

.slider.round:before {
	border-radius: 50%;
}

/* End Become a Driver page */
.driverform label {
	color: black;
}

.driverform input[type="submit"],
.driverform input[type="submit"]:hover {
	background-color: #ffa000;
	border-color: #ffa000;
	color: #ffa000;
	color: #fff !important;
}

.driverform input[type="submit"]:hover {
	background-color: #ffa000;
	color: #ffffff !important;
}

.whitebox input[type="text"],
.whitebox input[type="password"],
.whitebox input[type="email"],
.whitebox select,
.whitebox textarea {
	border: 1px solid #e8e8e8;
	background-attachment: fixed;
	color: rgb(141, 141, 141);
	font-weight: 500;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.whitebox .row.gtr-50.gtr-uniform>* {
	padding-top: 0.25em;
}

#header .loginform h3 {
	font-size: 1.25em;
	color: black;
	margin: 0px;
}

.loginform h3~span {
	display: inline-block;
	font-size: 14px;
}

.loginform .inputrow {
	margin: 10px 0px;
}

.loginform .inputrow input[type="number" i] {
	width: 100%;
	border: none;
	padding: 5px 10px;
}

.loginform .inputrow input {
	color: rgb(110, 110, 110);
	font-size: 14px;
}

.inputrow {
	margin: 5px 0px;
	border-bottom: 1px solid #eaeaea;
}

.otprow {
	text-align: center;
	margin-top: 30px;
}

.otp {
	display: flex;
}

.otp span {
	line-height: 48px;
	width: 50px;
}

.otp input {
	background: none;
	border: none;
	width: 100%;
}

.loginform .otp input[type="number" i] {
	padding: initial;
}

.login {
	background: #ffa000;
	padding: 5px 25px;
	text-align: center;
	font-size: 14px;
	line-height: 28px;
	color: white;
	cursor: pointer;
	margin: 16px 0px 6px;
}

.bottombtn {
	font-size: 13px;
	display: flex;
}

.bottombtn a {
	display: block;
	width: 100%;
	color: rgb(110, 110, 110);
}

.bottombtn a:hover {
	cursor: pointer;
	color: #000;
}

.bottombtn a:last-child {
	text-align: right;
}

.bottombtn span {
	width: 100%;
	float: left;
}

.bottombtn span a {
	display: inline;
}

.searator {
	display: flex;
	height: 10px;
	margin-top: 40px;
	width: 100%;
}

.searatorline {
	width: 50%;
	height: 1px;
	background: #d4d4d5;
}

.or {
	margin-top: -13px;
	padding: 0px 14px;
}

.smallheading {
	font-size: 14px;
	color: #000;
}

/* About us page */
.aboutpage {
	padding: 6em 4em 0em 0em;
	background-image: url(./images/about.png), url(./images/about-us.png);
	background-repeat: no-repeat;
	background-position: 95% 10%, bottom right;
	background-size: 50%, 100%;
	color: #636363;
}

.aboutpage h2 {
	color: #201e1e;
	font-size: 2em;
	margin: 0px;
	/* width: calc(100% - 500px); */
	line-height: 35px;
	font-weight: bold;
	margin-bottom: 10px;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.aboutpage .shortpara {
	width: calc(100% - 450px);
	display: inline-block;
	margin-bottom: 40px;
	font-size: 13px;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.aboutpage,
.listing {
	font-size: 15px;
	padding-bottom: 50px;
}

.listing h3 {
	font-weight: bold;
	font-size: 26px;
	color: #000;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.listing ul {
	margin: 0px;
	padding: 0px;
	list-style: none;
}

.listing ul li {
	list-style: none;
	margin-bottom: 12px;
	padding: 0px;
	font-size: 13px;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

}

.policies h3 {
	margin-top: 20px;
	margin-bottom: 5px;
}

.policies ul li {
	margin-bottom: 10px;
	font-size: 13px;
	line-height: 18px;
	list-style: disc;
	margin-left: 14px;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.listing ul li strong {
	font-size: 16px;
	color: #3a3a3a;
	font-weight: bold;
}

/* End About Us page */

.policies {

	padding: 6em 4em 2em 0em;
	background-image: url(./images/privacy.png);
	background-repeat: no-repeat;
	background-position: 95% 5%;
	background-size: 40%;
	color: #636363;

}

.policies h4 {
	color: #020202;
	font-weight: bold;
	margin-bottom: 5px;
	margin-top: 20px;

}

/* Footer */

#footer .icons {
	margin: 1em 0 0 0;
}

#footer .icons a {
	color: rgba(255, 255, 255, 0.4);
}

#footer .copyright {
	color: rgba(255, 255, 255, 0.4);
	font-size: 0.8em;
	list-style: none;
	margin: 1em 0 0 0;
	padding: 0;
}

#footer .copyright li {
	border-left: solid 1px rgba(255, 255, 255, 0.25);
	display: inline-block;
	line-height: 1em;
	margin-left: 0.75em;
	padding-left: 0.75em;
}

#footer .copyright li:first-child {
	border-left: 0;
	margin-left: 0;
	padding-left: 0;
}

#footer .copyright li a {
	color: inherit;
}

/* Main */

#main {
	margin-left: 30%;
	width: calc(100% - 30%);
}



#main>section {

	margin: 4em 0 0 0;
}

#main>section:first-child {
	border-top: 0;
	margin-top: 0;
	padding-top: 0;
}

#main #one,
#main #two,
#main #four {
	padding-left: 5%;
	padding-right: 5%;
}

.bottom-panel {
	padding: 0px 30px;
}

.bottom-panel {
	padding: 0px 30px;
}

.bottom-panel h3 {
	font-size: 16px;
	font-weight: bold;
	color: #ffa000;
	line-height: 0.1em;
}

.bottom-panel h2 {
	color: #201e1e;
	font-size: 1.5em;
	line-height: 1.5em;
}

.links {
	padding: 0px;
	list-style: none;
}

.links li {
	padding: 0;
}

.links a {
	font-size: 0.9em;
	line-height: 2em;
	margin: 0;
	color: #4a4a4a;
}

.links a:hover {
	cursor: pointer;
}


/* Poptrox */

@-moz-keyframes spin {
	0% {
		-moz-transform: rotate(0deg);
		-webkit-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	100% {
		-moz-transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
		-ms-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@-webkit-keyframes spin {
	0% {
		-moz-transform: rotate(0deg);
		-webkit-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	100% {
		-moz-transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
		-ms-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@-ms-keyframes spin {
	0% {
		-moz-transform: rotate(0deg);
		-webkit-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	100% {
		-moz-transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
		-ms-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes spin {
	0% {
		-moz-transform: rotate(0deg);
		-webkit-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		transform: rotate(0deg);
	}

	100% {
		-moz-transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
		-ms-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

.poptrox-popup {
	-moz-box-sizing: content-box;
	-webkit-box-sizing: content-box;
	-ms-box-sizing: content-box;
	box-sizing: content-box;
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0);
	background: #fff;
	border-radius: 0.35em;
	box-shadow: 0 0.1em 0.15em 0 rgba(0, 0, 0, 0.15);
	overflow: hidden;
	padding-bottom: 3em;
}

.poptrox-popup .loader {
	text-decoration: none;
	-moz-animation: spin 1s linear infinite;
	-webkit-animation: spin 1s linear infinite;
	-ms-animation: spin 1s linear infinite;
	animation: spin 1s linear infinite;
	font-size: 1.5em;
	height: 1em;
	left: 50%;
	line-height: 1em;
	margin: -0.5em 0 0 -0.5em;
	position: absolute;
	top: 50%;
	width: 1em;
}

.poptrox-popup .loader:before {
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	display: inline-block;
	font-style: normal;
	font-variant: normal;
	text-rendering: auto;
	line-height: 1;
	text-transform: none !important;
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
}

.poptrox-popup .loader:before {
	content: '\f1ce';
}

.poptrox-popup .caption {
	background: #fff;
	bottom: 0;
	cursor: default;
	font-size: 0.9em;
	height: 3em;
	left: 0;
	line-height: 2.8em;
	position: absolute;
	text-align: center;
	width: 100%;
	z-index: 1;
}

.poptrox-popup .nav-next,
.poptrox-popup .nav-previous {
	text-decoration: none;
	-moz-transition: opacity 0.2s ease-in-out;
	-webkit-transition: opacity 0.2s ease-in-out;
	-ms-transition: opacity 0.2s ease-in-out;
	transition: opacity 0.2s ease-in-out;
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0);
	background: rgba(0, 0, 0, 0.01);
	cursor: pointer;
	height: 100%;
	opacity: 0;
	position: absolute;
	top: 0;
	width: 50%;
}

.poptrox-popup .nav-next:before,
.poptrox-popup .nav-previous:before {
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	display: inline-block;
	font-style: normal;
	font-variant: normal;
	text-rendering: auto;
	line-height: 1;
	text-transform: none !important;
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
}

.poptrox-popup .nav-next:before,
.poptrox-popup .nav-previous:before {
	color: #fff;
	font-size: 2.5em;
	height: 1em;
	line-height: 1em;
	margin-top: -0.75em;
	position: absolute;
	text-align: center;
	top: 50%;
	width: 1.5em;
}

.poptrox-popup .nav-next {
	right: 0;
}

.poptrox-popup .nav-next:before {
	content: '\f105';
	right: 0;
}

.poptrox-popup .nav-previous {
	left: 0;
}

.poptrox-popup .nav-previous:before {
	content: '\f104';
	left: 0;
}

.poptrox-popup .closer {
	text-decoration: none;
	-moz-transition: opacity 0.2s ease-in-out;
	-webkit-transition: opacity 0.2s ease-in-out;
	-ms-transition: opacity 0.2s ease-in-out;
	transition: opacity 0.2s ease-in-out;
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0);
	color: #fff;
	height: 4em;
	line-height: 4em;
	opacity: 0;
	position: absolute;
	right: 0;
	text-align: center;
	top: 0;
	width: 4em;
	z-index: 2;
}

.poptrox-popup .closer:before {
	-moz-osx-font-smoothing: grayscale;
	-webkit-font-smoothing: antialiased;
	display: inline-block;
	font-style: normal;
	font-variant: normal;
	text-rendering: auto;
	line-height: 1;
	text-transform: none !important;
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
}

.poptrox-popup .closer:before {
	-moz-box-sizing: content-box;
	-webkit-box-sizing: content-box;
	-ms-box-sizing: content-box;
	box-sizing: content-box;
	border-radius: 100%;
	border: solid 3px rgba(255, 255, 255, 0.5);
	content: '\f00d';
	display: block;
	font-size: 1em;
	height: 1.75em;
	left: 50%;
	line-height: 1.75em;
	margin: -0.875em 0 0 -0.875em;
	position: absolute;
	top: 50%;
	width: 1.75em;
}

.poptrox-popup:hover .nav-next,
.poptrox-popup:hover .nav-previous {
	opacity: 0.5;
}

.poptrox-popup:hover .nav-next:hover,
.poptrox-popup:hover .nav-previous:hover {
	opacity: 1.0;
}

.poptrox-popup:hover .closer {
	opacity: 0.5;
}

.poptrox-popup:hover .closer:hover {
	opacity: 1.0;
}



/* XLarge */

@media screen and (max-width: 1800px) {

	/* Basic */

	body,
	input,
	select,
	textarea {
		font-size: 12pt;
	}

}

/* Large */

@media screen and (max-width: 1280px) {

	/* Header */

	#header {
		padding: 0px;
		width: 30%;
	}

	#header h1 {
		font-size: 1.25em;
	}

	#header h1 br {
		display: none;
	}

	#header>.forminner {
		margin-bottom: 0;
	}

	/* Footer */

	#footer .copyright li {
		border-left-width: 0;
		display: block;
		line-height: 2.25em;
		margin-left: 0;
		padding-left: 0;
	}



	/* Main */

	#main {
		margin-left: 30%;
		max-width: none;
		padding: 6em 3em 3em 3em;
		width: calc(100% - 30%);
	}

}

/* Medium */

@media screen and (max-width: 980px) {

	#collapsibleNavbar {
		position: initial;
	}

	/* Basic */

	h1 br,
	h2 br,
	h3 br,
	h4 br,
	h5 br,
	h6 br {
		display: none;
	}

	/* List */

	ul.icons li .icon {
		font-size: 1.25em;
	}

	/* Header */

	#header {
		background-attachment: scroll;
		background-position: top left, center center;
		background-size: auto, cover;
		left: auto;
		padding: 0px;
		position: relative;
		text-align: center;
		top: auto;
		width: 100%;
		display: block;
	}

	#header h1 {
		font-size: 1.75em;
	}

	#header h1 br {
		display: inline;
	}

	/* Footer */

	#footer {
		background-attachment: scroll;
		background-color: #1f1815;
		background-image: url("./images/overlay.png")
			/*, url("../../images/bg.jpg")*/
		;
		background-position: top left, bottom center;
		background-repeat: repeat, no-repeat;
		background-size: auto, cover;
		bottom: auto;
		left: auto;
		padding: 4em 4em 6em 4em;
		position: relative;
		text-align: center;
		width: 100%;
	}

	#footer .icons {
		margin: 0 0 1em 0;
	}

	#footer .copyright {
		margin: 0 0 1em 0;
	}

	#footer .copyright li {
		border-left-width: 1px;
		display: inline-block;
		line-height: 1em;
		margin-left: 0.75em;
		padding-left: 0.75em;
	}

	/* Main */

	#main {
		margin: 0;
		padding: 6em 4em;
		width: 100%;
	}

}

/* Small */

@media screen and (max-width: 736px) {

	/* Basic */

	h1 {
		font-size: 1.5em;
	}

	h2 {
		font-size: 1.2em;
	}

	h3 {
		font-size: 1em;
	}

	/* Section/Article */

	section.special,
	article.special {
		text-align: center;
	}

	header.major h2 {
		font-size: 1.35em;
	}

	/* List */

	ul.labeled-icons li {
		padding-left: 2em;
	}

	ul.labeled-icons li h3 {
		line-height: 1.75em;
	}

	/* Header */

	#header {
		padding: 0px;
	}

	#header h1 {
		font-size: 1.35em;
	}

	/* Footer */

	#footer {
		padding: 2.25em 1.5em;
	}

	/* Main */

	#main {
		padding: 2.25em 1.5em 0.25em 1.5em;
	}

	#main>section {
		margin: 2.25em 0 0 0;
		padding: 2.25em 0 0 0;
	}

	/* Poptrox */

	.poptrox-popup {
		border-radius: 0;
	}

	.poptrox-popup .nav-next:before,
	.poptrox-popup .nav-previous:before {
		margin-top: -1em;
	}

	#main {
		background-image: none;
	}

	.discription {
		width: 100%;
	}

	#main>section {
		margin: 4em 0 0 0;
		padding: 0em 0 0 0;
	}

	/* #main #one, #main #two, #main #four{
					padding-left: 0px; 
					padding-right: 0px;
				} */

	#main #one,
	#main #two {
		padding-left: 0px;
		padding-right: 0px;
	}

	.becomeDriver {
		background-image: none;
	}

	.amenities,
	.becomeDriver h2,
	.becomeDriver p {
		width: 100%
	}
}

/* XSmall */

@media screen and (max-width: 480px) {

	/* Header */

	#header {
		padding: 0px;
	}

	#header h1 br {
		display: none;
	}

	/* Footer */

	#footer .copyright li {
		border-left-width: 0;
		display: block;
		line-height: 2.25em;
		margin-left: 0;
		padding-left: 0;
	}

}

/*---- Contact Us ----*/
.address {
	padding: 2%;
}

.form-group i {
	padding: 10px;
}

/*---- End Contact Us ----*/
/*---- FAQ ----*/
.accordion {
	border-bottom: 1px solid #e8e8e8 !important;
	color: #444;
	cursor: pointer;
	padding: 10px;
	width: 100%;
	border: none;
	text-align: left;
	outline: none;
	font-size: 15px;
	transition: 0.4s;
	background: none;
}

.active,
.accordion:hover {
	background-color: #f7f7f7;
}

.carousel-item.active {
	background-color: transparent;
}

.panel {
	padding: 0 10px;
	display: none;
	background-color: white;
	overflow: hidden;
}

.panel p {
	font-weight: normal;
	font-size: 13px;
}

button.accordion:focus {
	outline: none;
}

button.accordion:after {
	content: '\002B';
	color: #777;
	font-weight: bold;
	float: right;
	margin-left: 5px;
}

button.accordion.active:after {
	content: "\2212";
}

/*---- End FAQ ----*/


/*CUSTOM NEW*/

section#about-cabyaari h2 {
	font-size: 34px;
	line-height: 40px;
	color: #000000;
	font-family: poppins;
	font-weight: 700;
}

section#about-cabyaari * {
	font-size: 15px;
	color: #2e2e2e;
	font-family: poppins;
	/* font-weight: 700; */
}