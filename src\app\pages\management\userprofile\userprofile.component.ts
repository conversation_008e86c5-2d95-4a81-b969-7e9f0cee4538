import { Component, OnInit, AfterViewChecked } from '@angular/core';
import { AppConfig } from 'src/app/configs/app.config';
import { AuthService, ApplicationUser } from 'src/app/shared/services';
import { LoggerService } from 'src/app/shared/interceptors/logger.service';

declare var $: any;

@Component({
  selector: 'app-userprofile',
  templateUrl: './userprofile.component.html',
  styleUrls: ['./userprofile.component.css']
})
export class UserprofileComponent implements OnInit, AfterViewChecked {

  constructor(
    private authService: AuthService) { 
    this.logger = LoggerService.createLogger('UserprofileComponent');
  }

  private logger: LoggerService;

  imageFolderPath: string = AppConfig.imageFolderPath;
  imageUrl: string = '../../../../assets/images/user.png';
  showPersonalInformation: boolean;

  user: ApplicationUser;

  ngOnInit() {
    this.initFields();
  }

  ngAfterViewChecked() {
    // Never print logs here it will print cotinuosly in console
    // this.logger.trace('ngAfterViewChecked() called');
    this.initToolTip();
  }

  private initToolTip = () => {
    // Never print logs here it will print cotinuosly in console as it is called from ngAfterViewChecked
    // this.logger.trace('initToolTip() called');
    $('[data-toggle="tooltip"]').tooltip();
  }

  private initFields = () => {
    this.showPersonalInformation = false;
    this.user = new ApplicationUser();
    this.authService.user$.subscribe((user) => {
      this.user = user;
      this.logger.trace('user change to', this.user);
    });
  }

  togglePersonalInformation = () => {
    this.logger.trace('togglePersonalInformation() called');
    this.showPersonalInformation = !this.showPersonalInformation;
    this.logger.trace('showPersonalInformation', this.showPersonalInformation);
  }

  browseProfile = () => {
    alert("hello");
  }

  uploadProfilePhoto = (fileSelect) => {
    this.logger.trace('uploadProfilePhoto() called with fileSelect', fileSelect);
    
    const file: File = fileSelect.files[0];
    this.logger.trace('file', file);
    
    if (!file) {
      this.logger.trace('file is undefied, no need to do anything else');
      return;
    }

    const reader = new FileReader();

    reader.addEventListener('load', (event: any) => {
      this.logger.trace('loaded event', event);

      const imageURL = event.target.result;
      if (imageURL === this.imageUrl) {
        this.logger.trace('imageURL Unchanged no need to do anything else');
        return;
      }

      this.setImageURL(imageURL);
      // this.selectedFile = new ImageSnippet(event.target.result, file);

      // this.imageService.uploadImage(this.selectedFile.file).subscribe(
      //   (res) => {
        
      //   },
      //   (err) => {
        
      //   })
    });

    reader.readAsDataURL(file);
  }

  setImageURL = (imageURL: string) => {
    this.logger.trace('setImageURL() called with imageURL', imageURL);
    this.imageUrl = imageURL;
  }

  logout = () => {
    this.logger.trace('logout() called');
    this.authService.logout();
  }
}
