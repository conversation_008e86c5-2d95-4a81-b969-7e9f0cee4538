import { ProgressService } from 'src/app/shared/services/progress.service';
import { Router } from '@angular/router';
import { SuccessMessage } from 'src/app/shared/constants/message.content';
import { AppConstants } from 'src/app/shared/constants/AppConstants';
import { UpdateBookingRequest } from './../models/updateBookingRequest';
import { BookingAPIService } from './booking-api.service';
import { WindowRefService, ICustomWindow } from './window-ref.service';
import { ErrorMessage } from './../constants/message.content';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from './auth.service';
import { Injectable, NgZone } from '@angular/core';
import { PaymentResponseService } from './payment-response.service';
import { LoggerService } from '../interceptors/logger.service';

@Injectable({
  providedIn: 'root'
})
export class RazorpayService {

  private options:any;
  private _window: ICustomWindow;
  private rzp: any;
  private currentUser: any;
  private logger: LoggerService;

  constructor(private authService: AuthService, 
    private zone: NgZone, 
    private toastrService: ToastrService,
    private progressService: ProgressService,
    private windowRef: WindowRefService,
    private paymentService: PaymentResponseService,
    private bookingAPIService: BookingAPIService,
    private router: Router) {
    this.logger = LoggerService.createLogger('RazorpayService');
    this._window = this.windowRef.nativeWindow;
    this.currentUser = this.authService.getCurrentUser();
  }

  initPay(orderID: string, bookingID: string): void {
    this.logger.debug("GetOrderID() function Calling....");
    this.logger.debug(""+this.currentUser.firstName);
    this.logger.debug(orderID, +"\n" + bookingID);
this.options = {
  order_id:orderID,
  key: AppConstants.RAZORPAY_KEY,
  name: AppConstants.RAZORPAY_TITLE,
  description: AppConstants.RAZORPAY_DESC,
  prefill: {
    name: this.currentUser.firstName,
    email: this.currentUser.email,
    contact: this.currentUser.phoneNumber
  },
  notes: {},
  theme: {
    color: AppConstants.RAZORPAY_COLOR
  },
  handler: res => { this. paymentHandler(res)},
  modal: {
    escape: false,
    ondismiss: (() => {
      this.zone.run(() => {
        this.showProgressBar();
        this.showBookingFailMessage();
        return false;
      })
    })
  }
};


    this.rzp = this.windowRef.nativeWindow['Razorpay'](this.options);
    this.rzp.open();
  }

  private showBookingFailMessage = () => {
    this.toastrService.error(AppConstants.EMPTY_STRING, ErrorMessage.BOOKING_FAILED);
    this.hideProgressBar();
  }

  private showBookingSuccessMessage = () => {
    this.toastrService.success(AppConstants.EMPTY_STRING, SuccessMessage.BOOKING_SUCCESSFULL);
  }

  private showProgressBar = () => {
    this.progressService.isPorgress.next(true);
  }

  private hideProgressBar = () => {
    this.progressService.isPorgress.next(false);
  }  

  paymentHandler= (res: any) => {
    this.showProgressBar();
    const updatedRequest: UpdateBookingRequest = {
      "paymentId": res.razorpay_payment_id,
      "razorpayOrderId": res.razorpay_order_id,
      "razorpaySignature": res.razorpay_signature
    }

    this.bookingAPIService.updateBooking(updatedRequest).subscribe(
      (response) => {
        this.hideProgressBar();
        this.logger.debug('updateBooking() response', response);

        if (response.succeeded) {
          this.showBookingSuccessMessage();
          
          this.paymentService.paymentResponseValue = response['data'];
          const bookingId =  this.paymentService.paymentResponseValue['bookingId'];

          const url = 'userprofile/booking-receipt/' + bookingId;
          this.logger.debug('url', url);
          this.router.navigate([url]);
        } else {
          this.logger.debug(response.message);
          this.logger.debug(response);
        }
      },
      error => {
        this.hideProgressBar();
        this.logger.error('updateBooking() error', error);
      }
    );
  }
}
