/* Custom jQuery UI Datepicker Overrides */
/* This file provides global overrides for jQuery UI datepicker styles */

/* Force override jQuery UI datepicker styles with maximum specificity */
div.ui-datepicker.ui-widget.ui-widget-content.ui-helper-clearfix.ui-corner-all {
  background-color: #fff !important;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1) !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem !important;
  border: none !important;
  font-family: 'Roboto', sans-serif !important;
  width: auto !important;
  min-width: 250px !important;
}

/* Override jQuery UI default width */
.ui-datepicker.ui-widget {
  width: auto !important;
  min-width: 250px !important;
}

/* Table styling */
.ui-datepicker table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  width: 100% !important;
  margin: 0 !important;
}

/* Header styling */
.ui-datepicker .ui-datepicker-header {
  position: relative !important;
  text-align: center !important;
  margin-bottom: 0.5rem !important;
  background: none !important;
  border: none !important;
  padding: 0.5rem 0 !important;
}

/* Navigation buttons */
.ui-datepicker .ui-datepicker-header a {
  cursor: pointer !important;
  position: absolute !important;
  top: 0 !important;
  width: 2rem !important;
  height: 2rem !important;
  margin: 0.5rem !important;
  border-radius: 0.25rem !important;
  transition: 0.3s all !important;
  text-indent: -9999px !important;
  border: none !important;
  background-color: transparent !important;
}

.ui-datepicker .ui-datepicker-header a:hover {
  background-color: #ECEFF1 !important;
}

/* Previous button */
.ui-datepicker .ui-datepicker-header .ui-datepicker-prev {
  left: 0 !important;
  background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMyIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDEzIDEzIj48cGF0aCBmaWxsPSIjNDI0NzcwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03LjI4OCA2LjI5NkwzLjIwMiAyLjIxYS43MS43MSAwIDAgMSAuMDA3LS45OTljLjI4LS4yOC43MjUtLjI4Ljk5OS0uMDA3TDguODAzIDUuOGEuNjk1LjY5NSAwIDAgMSAuMjAyLjQ5Ni42OTUuNjk1IDAgMCAxLS4yMDIuNDk3bC00LjU5NSA0LjU5NWEuNzA0LjcwNCAwIDAgMS0xLS4wMDcuNzEuNzEgMCAwIDEtLjAwNi0uOTk5bDQuMDg2LTQuMDg2eiIvPjwvc3ZnPg==") !important;
  background-repeat: no-repeat !important;
  background-size: 0.5rem !important;
  background-position: 50% !important;
  transform: rotate(180deg) !important;
}

/* Next button */
.ui-datepicker .ui-datepicker-header .ui-datepicker-next {
  right: 0 !important;
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMyIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDEzIDEzIj48cGF0aCBmaWxsPSIjNDI0NzcwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03LjI4OCA2LjI5NkwzLjIwMiAyLjIxYS43MS43MSAwIDAgMSAuMDA3LS45OTljLjI4LS4yOC43MjUtLjI4Ljk5OS0uMDA3TDguODAzIDUuOGEuNjk1LjY5NSAwIDAgMSAuMjAyLjQ5Ni42OTUuNjk1IDAgMCAxLS4yMDIuNDk3bC00LjU5NSA0LjU5NWEuNzA0LjcwNCAwIDAgMS0xLS4wMDcuNzEuNzEgMCAwIDEtLjAwNi0uOTk5bDQuMDg2LTQuMDg2eiIvPjwvc3ZnPg==') !important;
  background-repeat: no-repeat !important;
  background-size: 10px !important;
  background-position: 50% !important;
}

/* Hide button text */
.ui-datepicker .ui-datepicker-header a span {
  display: none !important;
}

/* Title styling */
.ui-datepicker .ui-datepicker-title {
  text-align: center !important;
  line-height: 2rem !important;
  margin-bottom: 0.25rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  padding-bottom: 0.25rem !important;
  color: #546E7A !important;
}

/* Calendar header */
.ui-datepicker .ui-datepicker-calendar thead th {
  padding: 0.25rem 0 !important;
  text-align: center !important;
  font-size: 0.75rem !important;
  font-weight: 400 !important;
  color: #78909C !important;
  background: none !important;
  border: none !important;
}

/* Calendar cells */
.ui-datepicker .ui-datepicker-calendar tbody td {
  width: 2.5rem !important;
  text-align: center !important;
  padding: 0 !important;
  border: none !important;
}

/* Date links */
.ui-datepicker .ui-datepicker-calendar tbody td a {
  display: block !important;
  border-radius: 0.25rem !important;
  line-height: 2rem !important;
  transition: 0.3s all !important;
  color: #546E7A !important;
  font-size: 0.875rem !important;
  text-decoration: none !important;
  border: none !important;
  background: none !important;
  width: 100% !important;
  height: 2rem !important;
}

/* Hover state */
.ui-datepicker .ui-datepicker-calendar tbody td a:hover {
  background-color: #E0F2F1 !important;
}

/* Active/selected state */
.ui-datepicker .ui-datepicker-calendar tbody td a.ui-state-active,
.ui-datepicker .ui-datepicker-calendar tbody td a.ui-state-highlight {
  background-color: #009688 !important;
  color: white !important;
}

/* Week column */
.ui-datepicker .ui-datepicker-week-col {
  color: #78909C !important;
  font-weight: 400 !important;
  font-size: 0.75rem !important;
}

/* Custom Time Picker Styles */
.time-picker {
  position: absolute !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 10px !important;
  background: #eeeeee !important;
  border-radius: 6px !important;
  margin-top: 10px !important;
  z-index: 1000 !important;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #ddd !important;
  min-width: 200px !important;
  white-space: nowrap !important;
}

.time-picker__select {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  outline: none !important;
  text-align: center !important;
  border: 1px solid #dddddd !important;
  border-radius: 6px !important;
  padding: 6px 10px !important;
  background: #ffffff !important;
  cursor: pointer !important;
  font-family: 'Roboto', sans-serif !important;
  margin: 0 2px !important;
  font-size: 14px !important;
  display: inline-block !important;
  vertical-align: middle !important;
}
