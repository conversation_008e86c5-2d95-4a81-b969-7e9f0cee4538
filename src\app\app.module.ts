import { ConfirmationPageComponent } from './shared/components/confirmation-page/confirmation-page.component';
import { PaymentCallbackComponent } from './shared/components/payment-callback/payment-callback.component';
import { BrowserModule } from '@angular/platform-browser';
import { NgModule, APP_INITIALIZER, Optional, SkipSelf } from '@angular/core';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { Error404PageComponent } from './pages/error404-page/error404-page.component';
import { APP_CONFIG, AppConfig} from './configs/app.config';
import { ENDPOINTS_CONFIG, EndpointsConfig} from './configs/endpoints.config';
import { ROUTES_CONFIG, RoutesConfig} from './configs/routes.config';
import { HeaderComponent} from './shared/components/header/header.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CablistComponent} from '../app/shared/components/cablist/cablist.component';
import { MostFavouriteRoutesComponent} from '../app/shared/components/most-favourite-routes/most-favourite-routes.component';
import { FormsModule , ReactiveFormsModule} from '@angular/forms';
import { OurServicesComponent} from './shared/components/our-services/our-services.component';

import { PromotionalBannersComponent} from '../app/shared/components/promotional-banners/promotional-banners.component';

import { SidemenuComponent} from '../app/shared/components/sidemenu/sidemenu.component';
import { LoginRegistrationComponent } from './pages/login-registration/login-registration.component';
import { UserDetailsComponent } from './pages/customers/user-details/user-details.component';
import { DriverDetailsComponent } from './pages/drivers/driver-details/driver-details.component';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpClientModule } from '@angular/common/http';

import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';

import {MatButtonModule} from '@angular/material/button';

import { MatIconModule} from '@angular/material/icon';
import { MatListModule} from '@angular/material/list';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSidenavModule} from '@angular/material/sidenav';
import { MatToolbarModule} from '@angular/material/toolbar';
import { RouterModule } from '@angular/router';
import { AboutUsComponent } from './pages/about-us/about-us.component';
import { FaqsComponent } from './pages/faqs/faqs.component';
import { ContactUsComponent } from './pages/contact-us/contact-us.component';
import { PrivacyAndPoliciesComponent } from './pages/privacy-and-policies/privacy-and-policies.component';
import { HomeComponent } from './pages/home/<USER>';
import { BecomeDriverComponent } from './pages/become-driver/become-driver.component';
import { PickDropDetailsComponent } from './shared/components/pick-drop-details/pick-drop-details.component';
import { PaymentBookingDetailsComponent } from './shared/components/payment-booking-details/payment-booking-details.component';
import { ToastrModule } from 'ngx-toastr';
import { CityRequestSharedService } from './shared/services/city-request-shared.service';
import { ProgressLoaderComponent } from './shared/components/progress-loader/progress-loader.component';
import { ProgressService } from './shared/services/progress.service';
import { AuthService } from './shared/services';
import { appInitializer } from './shared/constants/appInitializer';
import { UnauthorizedInterceptor } from './shared/interceptors/unauthorized.interceptor';
import { UserprofileComponent } from './pages/management/userprofile/userprofile.component';
import { JwtInterceptor } from './shared/interceptors/JwtInterceptor';
import { RegistrationComponent } from './pages/registration/registration.component';
import { SpinnerComponent } from './shared/components/spinner/spinner.component';
import { SizeDetectorComponent } from './shared/components/size-detector/size-detector.component';
import { ResizeService } from './shared/services/resize.service';
import { UserRidesComponent } from './pages/management/user-rides/user-rides.component';
import { FooterComponent } from './shared/components/footer/footer.component';
import { TermsAndConditionsComponent } from './pages/terms-and-conditions/terms-and-conditions.component';
import { RefundPolicyComponent } from './pages/refund-policy/refund-policy.component';

@NgModule({
  declarations: [
    AppComponent,
    AboutUsComponent,
    PickDropDetailsComponent,
    PaymentBookingDetailsComponent,
    FaqsComponent,
    HomeComponent,
    BecomeDriverComponent,
    Error404PageComponent,
    ContactUsComponent,
    ConfirmationPageComponent,
    PaymentCallbackComponent,
    PrivacyAndPoliciesComponent,
    HeaderComponent,
    CablistComponent,
    MostFavouriteRoutesComponent,
    OurServicesComponent,
    PromotionalBannersComponent,
    SidemenuComponent,
    Error404PageComponent,
    LoginRegistrationComponent,
    UserDetailsComponent,
    DriverDetailsComponent,
    HomeComponent,
    ProgressLoaderComponent,
    UserprofileComponent,
    RegistrationComponent,
    SpinnerComponent,
    SizeDetectorComponent,
    UserRidesComponent,
    FooterComponent,
    RefundPolicyComponent,
    TermsAndConditionsComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    RouterModule,
    BrowserAnimationsModule,
    HttpClientModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatAutocompleteModule,
    MatNativeDateModule,
    MatToolbarModule,
    MatSidenavModule,
    MatListModule,
    MatButtonModule,
    MatIconModule,
    NgbModule,
    ToastrModule.forRoot()
  ],
  providers: [
    ProgressService,
    CityRequestSharedService,
    ResizeService,
    {provide: APP_CONFIG, useValue: AppConfig},
    {provide: ROUTES_CONFIG, useValue: RoutesConfig},
    {provide: ENDPOINTS_CONFIG, useValue: EndpointsConfig},
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializer,
      multi: true,
      deps: [AuthService],
    },
    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: UnauthorizedInterceptor,
      multi: true,
    },
  ],
  bootstrap: [AppComponent]
})
export class AppModule { constructor(@Optional() @SkipSelf() core: AppModule) {
  if (core) {
    throw new Error('Core Module can only be imported to AppModule.');
  }
}}
