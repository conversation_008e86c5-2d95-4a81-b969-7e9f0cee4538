<app-progress-loader></app-progress-loader>

<app-sidemenu></app-sidemenu> 

<header id="header" class="login-header">
    <span class="toggle menu-toggle"> <img src="{{imageFolderPath}}/toggle-icon.png"> </span>
    <div class="topheader loginheader"><a routerLink="/home" class="logo"><img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;" /></a>
       
    </div>
    <div class="loginform">
        <div class="col-12 col-12-small">
            <form method="post" action="#">
                <div class="row gtr-50">
                    <div class="col-12">
                        <h3>Log in to our website </h3>
                        <!-- <span>Login to our website</span> -->
                        <div class="inputrow">
                            <input type="text" id="userName" name="username" [(ngModel)]="username" class="form-control"
                                placeholder="Email ID" required autofocus />
                        </div>
                        <div class="inputrow">
                            <input type="password" id="inputPassword" name="password" [(ngModel)]="password"
                                class="form-control" placeholder="Password" required />
    
                        </div>
                        <div class="checkbox mb-3 text-danger" *ngIf="loginError">
                            Login failed. Please try again.
                        </div>
                        <div class="login" (click)="login()">Log in</div>
                        <div class="bottombtn">
                            <a>Forgot Password?</a>
                            <a [routerLink]="['/register']" [queryParams]="{ returnUrl: returnUrl }">Register Now</a>
                        </div>
                    </div>
    
                    <div class="searator">
                        <div class="searatorline"></div>
                        <span class="or">OR</span>
                        <div class="searatorline"></div>
                    </div>
    
                    <div class="col-12 otprow">
                        <span class="smallheading">Login with OTP</span>
                        <div class="inputrow otp"><span><input type="number" name="counrtyCode" id="counrtyCode"
                                    placeholder="+91" /></span>
                            <input type="number" name="name" id="name" placeholder="Mobile Number" /></div>
                        <div class="inputrow otp" *ngIf="isOTPSend===true"> <input type="number" name="name" id="otp"
                                placeholder="OTP" /></div>
                        <div class="login" *ngIf="isOTPSend===false" (click)="GetOTP()">Get OTP</div>
                        <div class="login" *ngIf="isOTPSend===true" (click)="login()">Login</div>
                        <div class="bottombtn" *ngIf="isOTPSend===true">
                            <!-- <a href="#">Forgot Password?</a> -->
                            <a (click)="GetOTP()">Resend Again</a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</header>

<div id="main" class="loginpage d-none d-lg-block col-lg-12">
    <section id="two" class="logincontent"></section>
</div>