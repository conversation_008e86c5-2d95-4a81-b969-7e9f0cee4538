import { Injectable, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { WindowRefService, ICustomWindow } from './window-ref.service';
import { ProgressService } from './progress.service';
import { AuthService } from './auth.service';
import { PaymentResponseService } from './payment-response.service';
import { LoggerService } from '../interceptors/logger.service';
import { AppConstants } from '../constants/AppConstants';
import { SuccessMessage, ErrorMessage } from '../constants/message.content';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AppConfig } from 'src/app/configs/app.config';
import { BookingRequest } from '../models/booking.model';

// Base booking interface without Razorpay parameters
interface BaseBookingRequest {
  tripType: string;
  duration: string;
  distance: number;
  basicFare: number;
  fixRateNote: string;
  gst: number;
  gstFare: number;
  driverCharge: number;
  couponCode: string;
  couponDiscount: number;
  travelerName: string;
  phoneNumber: string;
  mailId: string;
  paymentMode: number;
  bookingCreatedBy: string;
  paymentOption: number;
  fare: number;
  perKMCharges: string;
  cashAmountToPayDriver: number;
  pickUpCity: string;
  dropOffCity: string;
  carCategory: string;
  carFeatures: string;
  carImage: string;
  carCapacity: number;
  pickUpAddress: string;
  dropOffAddress: string;
  pickUpAddressLongLat: string;
  dropOffAddressLongLat: string;
  pickUpDate: string;
  pickUpTime: string;
  tollCharge: number;
}

// Types for PhonePe API
interface PhonePeTokenRequest extends BaseBookingRequest {
  merchantUserId: string;
  callbackUrl: string;
}

interface PhonePeTokenResponse {
  succeeded: boolean;
  message: string;
  data: {
    tokenUrl: string;
    merchantTransactionId: string;
    orderId: string;
  };
}

interface PhonePeVerifyRequest {
  merchantTransactionId: string;
  orderId: string;
}

interface PhonePeVerifyResponse {
  succeeded: boolean;
  message: string;
  errors: string | null;
  data: {
    paymentStatus: string;
    transactionId: string;
    orderId: string;
    bookingId: string | null;
    amount: number;
    paymentId: string;
    paymentType: string;
    partialPaymentAmount: number | null;
    remainingAmountForDriver: number | null;
  };
}

declare global {
  interface Window {
    PhonePeCheckout: {
      transact: (options: { tokenUrl: string; callback: (response: string) => void; type: string }) => void;
      closePage: () => void;
    };
    phonepeScriptLoaded: boolean;
  }
}

@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  private _window: ICustomWindow;
  private currentUser: any;
  private logger: LoggerService;

  private readonly PHONEPE_API_BASE = `${AppConfig.CabYaari_WebAPI_New}/api/v1/payments/phonepe`;

  private getHttpOptions() {
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      })
    };
  }

  constructor(
    private authService: AuthService,
    private zone: NgZone,
    private toastrService: ToastrService,
    private progressService: ProgressService,
    private windowRef: WindowRefService,
    private paymentService: PaymentResponseService,
    private http: HttpClient,
    private router: Router
  ) {
    this.logger = LoggerService.createLogger('PaymentService');
    this._window = this.windowRef.nativeWindow;
    this.currentUser = this.authService.getCurrentUser();
  }

  initPayment(): void {
    // Get PhonePe token URL from backend
    this.getPhonePeToken().subscribe(
      (tokenResponse) => {
        if (!tokenResponse.succeeded) {
          this.logger.error('Failed to get PhonePe token:', tokenResponse.message);
          this.showPaymentFailedMessage();
          return;
        }

        this.showProgressBar();

        // Store transaction data in localStorage for retrieval after redirect
        const transactionData = {
          merchantTransactionId: tokenResponse.data.merchantTransactionId,
          orderId: tokenResponse.data.orderId,
          timestamp: Date.now()
        };
        localStorage.setItem('phonepe_transaction_data', JSON.stringify(transactionData));

        try {
          // Redirect to PhonePe payment page
          this.logger.debug('Redirecting to PhonePe payment URL:', tokenResponse.data.tokenUrl);
          window.location.href = tokenResponse.data.tokenUrl;

        } catch (error) {
          this.logger.error('Error initiating PhonePe payment redirect:', error);
          this.hideProgressBar();
          this.showPaymentFailedMessage();
          // Clean up stored transaction data on error
          localStorage.removeItem('phonepe_transaction_data');
        }
      },
      (error) => {
        this.logger.error('Error getting PhonePe token:', error);
        this.hideProgressBar();
        this.showPaymentFailedMessage();
      }
    );
  }



  private getPhonePeToken(): Observable<PhonePeTokenResponse> {
    // Get the booking request data
    const bookingRequest = this.paymentService.bookingRequestData;
    
    // Create the token request with all booking details (excluding Razorpay parameters)
    const request: PhonePeTokenRequest = {
      // Booking details
      tripType: bookingRequest.tripType,
      duration: bookingRequest.duration,
      distance: bookingRequest.distance,
      basicFare: bookingRequest.basicFare,
      fixRateNote: bookingRequest.fixRateNote,
      gst: bookingRequest.gst,
      gstFare: bookingRequest.gstFare,
      driverCharge: bookingRequest.driverCharge,
      couponCode: bookingRequest.couponCode,
      couponDiscount: bookingRequest.couponDiscount,
      travelerName: bookingRequest.travelerName,
      phoneNumber: bookingRequest.phoneNumber,
      mailId: bookingRequest.mailId,
      paymentMode: bookingRequest.paymentMode,
      bookingCreatedBy: bookingRequest.bookingCreatedBy,
      paymentOption: bookingRequest.paymentOption,
      fare: bookingRequest.fare,
      perKMCharges: bookingRequest.perKMCharges,
      cashAmountToPayDriver: bookingRequest.cashAmountToPayDriver,
      pickUpCity: bookingRequest.pickUpCity,
      dropOffCity: bookingRequest.dropOffCity,
      carCategory: bookingRequest.carCategory,
      carFeatures: bookingRequest.carFeatures,
      carImage: bookingRequest.carImage,
      carCapacity: bookingRequest.carCapacity,
      pickUpAddress: bookingRequest.pickUpAddress,
      dropOffAddress: bookingRequest.dropOffAddress,
      pickUpAddressLongLat: bookingRequest.pickUpAddressLongLat,
      dropOffAddressLongLat: bookingRequest.dropOffAddressLongLat,
      pickUpDate: bookingRequest.pickUpDate,
      pickUpTime: bookingRequest.pickUpTime,
      tollCharge: bookingRequest.tollCharge,

      merchantUserId: this.currentUser.id,
      callbackUrl: `${window.location.origin}/#/payment-callback`
    };

    this.logger.debug('Getting PhonePe token with request:', request);
    return this.http.post<PhonePeTokenResponse>(
      `${this.PHONEPE_API_BASE}/token`, 
      request, 
      this.getHttpOptions()
    );
  }

  private verifyPayment(verifyRequest: PhonePeVerifyRequest): void {
    this.logger.debug('Verifying payment with request:', verifyRequest);
    this.http.post<PhonePeVerifyResponse>(
      `${this.PHONEPE_API_BASE}/verify`,
      verifyRequest,
      this.getHttpOptions()
    ).subscribe(
      (response) => {
        this.hideProgressBar();
        this.logger.debug('Payment verification response:', response);

        if (!response.succeeded || (response.data.paymentStatus !== 'COMPLETED' && response.data.paymentStatus !== 'Paid')) {
          this.logger.error('Payment verification failed:', response.message);
          this.logger.error('Payment status received:', response.data?.paymentStatus);
          this.showPaymentFailedMessage();
          return;
        }

        // Payment verification successful - booking is already updated by the verify API
        this.showPaymentSuccessMessage();
        this.paymentService.paymentResponseValue = {
          data: {
            transactionId: response.data.transactionId,
            bookingId: response.data.bookingId,
            paymentStatus: response.data.paymentStatus,
            paymentType: response.data.paymentType,
            partialPaymentAmount: response.data.partialPaymentAmount,
            remainingAmountForDriver: response.data.remainingAmountForDriver
          },
          errors: response.errors || '',
          message: response.message,
          succeeded: response.succeeded
        };

        // Handle navigation - check if bookingId is available
        if (response.data.bookingId) {
          const bookingId = response.data.bookingId;
          this.router.navigate(['userprofile/booking-receipt/' + bookingId]);
        } else {
          // If no bookingId, show success message and navigate to home or bookings page
          this.logger.error('Payment successful but no bookingId received');
          this.router.navigate(['userprofile/bookings']);
        }
      },
      (error) => {
        this.hideProgressBar();
        this.logger.error('Error verifying payment:', error);
        this.showPaymentFailedMessage();
      }
    );
  }

  // Public method to verify payment from redirect callback
  verifyPaymentFromCallback(merchantTransactionId: string, orderId: string): void {
    this.verifyPayment({
      merchantTransactionId: merchantTransactionId,
      orderId: orderId
    });
  }

  // Method to get stored transaction data
  getStoredTransactionData(): { merchantTransactionId: string; orderId: string; timestamp: number } | null {
    try {
      const storedData = localStorage.getItem('phonepe_transaction_data');
      if (storedData) {
        const transactionData = JSON.parse(storedData);
        // Check if data is not too old (1 hour timeout)
        const oneHour = 60 * 60 * 1000;
        if (Date.now() - transactionData.timestamp < oneHour) {
          return transactionData;
        } else {
          // Clean up expired data
          localStorage.removeItem('phonepe_transaction_data');
          console.log('Stored transaction data expired');
        }
      }
    } catch (error) {
      this.logger.error('Error retrieving stored transaction data:', error);
      localStorage.removeItem('phonepe_transaction_data');
    }
    return null;
  }

  // Method to clear stored transaction data
  clearStoredTransactionData(): void {
    localStorage.removeItem('phonepe_transaction_data');
  }





  private showPaymentSuccessMessage(): void {
    this.toastrService.success(AppConstants.EMPTY_STRING, SuccessMessage.BOOKING_SUCCESSFULL);
  }

  private showPaymentFailedMessage(): void {
    this.toastrService.error(AppConstants.EMPTY_STRING, ErrorMessage.PAYMENT_FAILED);
  }

  private showProgressBar(): void {
    this.progressService.isPorgress.next(true);
  }

  private hideProgressBar(): void {
    this.progressService.isPorgress.next(false);
  }
} 