import { AuthService } from '../services/auth.service';
import { LoggerService } from '../interceptors/logger.service';


export function appInitializer(authService: AuthService) {
  const logger: LoggerService = LoggerService.createLogger('appInitializer');

  return () =>
    new Promise((resolve) => {
      logger.debug('refresh token on app start up')
      authService.refreshToken().subscribe().add(resolve);
    });
}
