import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { SCREEN_SIZE } from '../constants/screen-size.enum';
import { distinctUntilChanged } from 'rxjs/operators';
import { LoggerService } from '../interceptors/logger.service';

@Injectable()
export class ResizeService {

  constructor() {
    this.logger = LoggerService.createLogger('ResizeService');
    this.resizeSubject = new Subject();
  }

  private logger: LoggerService;
  private resizeSubject: Subject<SCREEN_SIZE>;

  onResize = (): Observable<SCREEN_SIZE> => {
    return this.resizeSubject.asObservable().pipe(distinctUntilChanged());
  }

  changeSize = (size: SCREEN_SIZE) => {
    this.logger.trace('changeSize() called with size', size);
    this.resizeSubject.next(size);
  }
}

