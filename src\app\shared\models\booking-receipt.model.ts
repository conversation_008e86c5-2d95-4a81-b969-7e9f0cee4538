export interface BookingReceiptData {
  bookingId: string;
  tripType: string;
  pickUpCity: string;
  dropOffCity: string;
  pickUpAddress: string;
  dropOffAddress: string;
  pickUpDate: string;
  pickUpTime: string;
  carCategory: string;
  carFeatures: string;
  carCapacity: number;
  distance: number;
  duration: string;
  basicFare: number;
  gst: number;
  totalFare: number;
  paymentType: string;
  paymentStatus: string;
  amountPaid: number;
  remainingAmountForDriver: number;
  travelerName: string;
  phoneNumber: string;
  mailId: string;
}

export interface BookingReceiptResponse {
  succeeded: boolean;
  message: string;
  errors: string | null;
  data: BookingReceiptData;
}
