import { Component, OnInit , Input} from '@angular/core';
import { CompanyGenericService } from '../../services/company-generic.service';
import { CarCategoryResponse } from '../../models/car-category-response';
import { HttpErrorResponse } from '@angular/common/http';
import { CarCategoryAttribute } from '../../models/car-category-attributes';
import { LoggerService } from '../../interceptors/logger.service';

@Component({
  selector: 'app-cablist',
  templateUrl: './cablist.component.html',
  styleUrls: ['./cablist.component.css']
})
export class CablistComponent implements OnInit {
  constructor(private companyGenericService: CompanyGenericService) { 
    this.logger = LoggerService.createLogger('CablistComponent');
  }

  @Input() imageFolderPath: string;
  
  private logger: LoggerService;

  carCategoryFareList : CarCategoryAttribute;

  ngOnInit(): void {
    this.getCarCategoryFareList();
  }

  getCarCategoryFareList()
  {
    this.companyGenericService.getCarCategoryRateFeatures().subscribe(
      (response: CarCategoryResponse) => {
      this.carCategoryFareList = response.data;
        this.logger.trace("carcate", this.carCategoryFareList);

      }, 
      (error: HttpErrorResponse) => {
        this.logger.error(error.error);
      }
    );
  }

}
