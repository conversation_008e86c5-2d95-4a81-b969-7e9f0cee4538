import { Component, OnInit } from '@angular/core';
import { ProgressService } from '../../services/progress.service';
import { LoggerService } from '../../interceptors/logger.service';

@Component({
  selector: 'app-progress-loader',
  templateUrl: './progress-loader.component.html',
  styleUrls: ['./progress-loader.component.css']
})
export class ProgressLoaderComponent implements OnInit {
  constructor(private progresService: ProgressService) {
    this.logger = LoggerService.createLogger('ProgressLoaderComponent');
    this.progresService.isPorgress.subscribe((v) =>{
      this.progress = v;
      this.logger.trace('progress', this.progress);
    });
  }

  private logger: LoggerService;

  progress: boolean;

  ngOnInit(): void { }
}
