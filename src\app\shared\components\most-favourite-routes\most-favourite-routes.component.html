<app-size-detector></app-size-detector>

<app-spinner #spinner></app-spinner>

<section *ngIf="favouriteRoutes?.length > 0" id="four">
  <header class="major">
    <h2>Most Favourite Routes</h2>
    <span>Find Your Car For Safe Journey</span>
  </header>

  <div id="routesCarouselControl" class="carousel slide" data-ride="carousel">
    <div class="carousel-inner">
      <div 
        *ngFor="let routes of groupedFavouriteRoutes; let i = index" 
        class="carousel-item"
        [class.active]="i === 0">
        <div class="row p-1">
          <article 
            *ngFor="let route of routes" 
            class="col-sm-4 cards">
            <div class="image fit thumb">
              <span class="roundShap">
                <img src="{{imageFolderPath}}/{{route?.CityImage}}" alt="{{route?.CityImage}}" />
              </span>
              
              <h3>{{route?.RouteName}}</h3>
              
              <p>{{route?.Features}}</p>
              
              <div class="prices">
                <i class="fa fa-rupee-sign"></i>
                {{route?.Fare}}
              </div>
              
              <a class="booknow text-white">Book Now</a>
            </div>
          </article>
        </div>
      </div>
    </div>
  
    <div class="actions text-center">
      <span class="button-circle" href="#routesCarouselControl" data-slide="prev">
        <i class="fa fa-chevron-left"></i>
      </span>

      <span class="button-circle" href="#routesCarouselControl" data-slide="next">
        <i class="fa fa-chevron-right"></i>
      </span>
    </div>  
  </div>
  
</section>