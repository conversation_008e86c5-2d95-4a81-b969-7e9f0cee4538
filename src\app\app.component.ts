import { Component, OnInit } from '@angular/core';
import { AppConfig } from './configs/app.config';
import { MmiService } from './shared/services/mmi/mmi-service.service';
import { Router } from '@angular/router';
import { AuthService } from './shared/services';

declare var $: any;

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {

  constructor(
    private router: Router, 
    public mmiService: MmiService, 
    public authService: AuthService) {}
  
  title = 'CabYaari.com';
  
  private token: string;
  
  imageFolderPath: string = AppConfig.imageFolderPath;
  errorMessage: string;

  ngOnInit(): void { 
    $(document).ready(function() {
      // @ts-ignore
      jQuery.fn.carousel.Constructor.TRANSITION_DURATION = 500  // 1 second
    });
  }

  logout() {
    this.authService.logout();
  }

  hasRoute(route: string) {
    return this.router.url.includes(route);
  }
}
