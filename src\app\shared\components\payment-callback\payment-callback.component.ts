import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { LoggerService } from '../../interceptors/logger.service';
import { PaymentService } from '../../services/payment.service';

@Component({
  selector: 'app-payment-callback',
  templateUrl: './payment-callback.component.html',
  styleUrls: ['./payment-callback.component.css']
})
export class PaymentCallbackComponent implements OnInit {
  private logger: LoggerService;
  
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private toastrService: ToastrService,
    private paymentService: PaymentService
  ) {
    this.logger = LoggerService.createLogger('PaymentCallbackComponent');
  }

  ngOnInit(): void {
    this.logger.debug('Payment callback component initialized');

    // Get query parameters from the callback URL
    this.route.queryParams.subscribe(params => {
      this.logger.debug('Payment callback params:', params);

      // Check if this is a PhonePe redirect callback
      if (this.isPhonePeCallback(params)) {
        this.handlePhonePeCallback(params);
      } else if (params['status']) {
        // Handle other payment callback parameters (legacy support)
        this.handlePaymentCallback(params);
      } else {
        // Check if we have stored transaction data for PhonePe redirect
        const storedData = this.paymentService.getStoredTransactionData();
        if (storedData) {
          this.logger.debug('Found stored transaction data, verifying payment');
          this.handlePhonePeRedirectReturn(storedData);
        } else {
          // If no parameters and no stored data, redirect to home
          this.logger.error('No payment callback parameters or stored transaction data found');
          this.router.navigate(['/home']);
        }
      }
    });
  }

  private handlePaymentCallback(params: any): void {
    const status = params['status'];
    const transactionId = params['transactionId'];
    const bookingId = params['bookingId'];
    
    this.logger.debug('Handling payment callback with status:', status);
    
    switch (status?.toLowerCase()) {
      case 'success':
      case 'completed':
        this.handleSuccessCallback(bookingId, transactionId);
        break;
        
      case 'failed':
      case 'failure':
        this.handleFailureCallback(transactionId);
        break;
        
      case 'cancelled':
      case 'cancel':
        this.handleCancelCallback(transactionId);
        break;
        
      default:
        this.logger.error('Unknown payment status:', status);
        this.toastrService.warning('Unknown payment status received');
        this.router.navigate(['/home']);
        break;
    }
  }

  private handleSuccessCallback(bookingId: string, transactionId: string): void {
    this.logger.debug('Payment successful:', { bookingId, transactionId });
    this.toastrService.success('Payment completed successfully!');
    
    if (bookingId) {
      // Navigate to booking receipt page
      this.router.navigate(['/userprofile/booking-receipt', bookingId]);
    } else {
      // Navigate to user bookings page
      this.router.navigate(['/userprofile/overview']);
    }
  }

  private handleFailureCallback(transactionId: string): void {
    this.logger.debug('Payment failed:', { transactionId });
    this.toastrService.error('Payment failed. Please try again.');
    this.router.navigate(['/home']);
  }

  private handleCancelCallback(transactionId: string): void {
    this.logger.debug('Payment cancelled:', { transactionId });
    this.toastrService.info('Payment was cancelled by user');
    this.router.navigate(['/home']);
  }

  private isPhonePeCallback(params: any): boolean {
    // Check if this looks like a PhonePe callback based on common parameters
    return params['code'] || params['transactionId'] || params['merchantId'] || params['checksum'];
  }

  private handlePhonePeCallback(params: any): void {
    this.logger.debug('Handling PhonePe callback with params:', params);

    // Get stored transaction data
    const storedData = this.paymentService.getStoredTransactionData();
    if (!storedData) {
      this.logger.error('No stored transaction data found for PhonePe callback');
      this.toastrService.error('Payment verification failed. Please try again.');
      this.router.navigate(['/home']);
      return;
    }

    // Check if payment was successful based on PhonePe parameters
    const code = params['code'];
    if (code === 'PAYMENT_SUCCESS') {
      this.logger.debug('PhonePe payment successful, verifying with backend');
      this.paymentService.verifyPaymentFromCallback(storedData.merchantTransactionId, storedData.orderId);
      this.paymentService.clearStoredTransactionData();
    } else if (code === 'PAYMENT_DECLINED' || code === 'PAYMENT_ERROR') {
      this.logger.debug('PhonePe payment failed:', code);
      this.toastrService.error('Payment failed. Please try again.');
      this.paymentService.clearStoredTransactionData();
      this.router.navigate(['/home']);
    } else if (code === 'PAYMENT_CANCELLED') {
      this.logger.debug('PhonePe payment cancelled by user');
      this.toastrService.info('Payment was cancelled by user');
      this.paymentService.clearStoredTransactionData();
      this.router.navigate(['/home']);
    } else {
      // For any other case, try to verify the payment
      this.logger.debug('Unknown PhonePe callback code, attempting verification:', code);
      this.paymentService.verifyPaymentFromCallback(storedData.merchantTransactionId, storedData.orderId);
      this.paymentService.clearStoredTransactionData();
    }
  }

  private handlePhonePeRedirectReturn(storedData: { merchantTransactionId: string; orderId: string; timestamp: number }): void {
    this.logger.debug('Handling PhonePe redirect return with stored data:', storedData);

    // When user returns from PhonePe without specific callback parameters,
    // we need to verify the payment status with our backend
    this.toastrService.info('Verifying payment status...');
    this.paymentService.verifyPaymentFromCallback(storedData.merchantTransactionId, storedData.orderId);
    this.paymentService.clearStoredTransactionData();
  }
}
