import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { AppConfig } from 'src/app/configs/app.config';
import { AboutUs } from 'src/app/shared/models/about_model';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AboutService {

  webAPIEndPoint:string='';
  constructor(private httpClient: HttpClient) {
    this.webAPIEndPoint = AppConfig.CabYaari_WebAPI;
  }



  getAboutUs(): Observable<AboutUs> {
    let apiURL = this.webAPIEndPoint+'/api/Home/cabyaari/about_us';
    return this.httpClient.get<AboutUs>(
      apiURL
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        throw error;
      })
    )
  }
}
