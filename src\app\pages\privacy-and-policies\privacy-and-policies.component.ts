import { Component, OnInit } from '@angular/core';
import { PrivacyService } from './services/privacy.service';
import { PrivacyPolicy } from 'src/app/shared/models/privacy_policy_model';
import { HttpErrorResponse } from '@angular/common/http';
import { AppConfig } from 'src/app/configs/app.config';

@Component({
  selector: 'app-privacy-and-policies',
  templateUrl: './privacy-and-policies.component.html',
  styleUrls: ['./privacy-and-policies.component.css']
})
export class PrivacyAndPoliciesComponent implements OnInit {

  
  privacies: any;

  constructor(private aboutService: PrivacyService) { }

  ngOnInit(): void {
    this.Privacy();
  }

  imageFolderPath: string = AppConfig.imageFolderPath;
  
  private Privacy(): void {

    this.aboutService.getPrivacyPolicy().subscribe(
          (response: PrivacyPolicy) => 
          {
            this.privacies = response as PrivacyPolicy;
            console.log(this.privacies);
         }, (error: HttpErrorResponse) => {
          console.log(error.error);
        });
  }

}
