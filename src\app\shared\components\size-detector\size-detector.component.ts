import { Component, AfterViewInit, HostListener, ElementRef } from '@angular/core';
import { SCREEN_SIZE } from '../../constants/screen-size.enum';
import { ResizeService } from '../../services/resize.service';
import { LoggerService } from '../../interceptors/logger.service';

@Component({
  selector: 'app-size-detector',
  templateUrl: './size-detector.component.html',
  styleUrls: ['./size-detector.component.css']
})
export class SizeDetectorComponent implements AfterViewInit {
  constructor(
    private elementRef: ElementRef,
    private resizeService: ResizeService) {
    this.logger = LoggerService.createLogger('SizeDetectorComponent');
  }

  private logger: LoggerService;

  prefix = 'is-';
  sizes = [
    { id: SCREEN_SIZE.XS, name: 'xs', css: `d-block d-sm-none` },
    { id: SCREEN_SIZE.SM, name: 'sm', css: `d-none d-sm-block d-md-none` },
    { id: SCREEN_SIZE.MD, name: 'md', css: `d-none d-md-block d-lg-none` },
    { id: SCREEN_SIZE.LG, name: 'lg', css: `d-none d-lg-block d-xl-none` },
    { id: SCREEN_SIZE.XL, name: 'xl', css: `d-none d-xl-block` },
  ];

  ngAfterViewInit() {
    this.detectScreenSize();
  }

  @HostListener("window:resize", [])
  onResize() {
    this.logger.trace('onResize() called');
    this.detectScreenSize();
  }

  private detectScreenSize() {
    this.logger.trace('detectScreenSize() called');
    // we will write this logic later

    const currentSize = this.sizes.find(x => {
      // get the HTML element
      const el = this.elementRef.nativeElement.querySelector(`.${this.prefix}${x.id}`);
      // check its display property value
      const isVisible = window.getComputedStyle(el).display != 'none';
      return isVisible;
    });

    this.resizeService.changeSize(currentSize.id);
  }

}
