import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CityRequestSharedService {


  private sharedData: any;
  private cityRequest = new BehaviorSubject(this.sharedData);
  sharedInformations = this.cityRequest.asObservable();

  constructor() { }

  nextData(cityRequest: any) {
    this.cityRequest.next(cityRequest);
  }


  getData()
  {
    return this.cityRequest.value;
  }
}
