/* User Rides Component Styles */
.user-rides {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Header Section */
#header-section {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content h2 {
  margin: 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.refresh-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.refresh-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Loading State */
.loading-container {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.spinner {
  margin-bottom: 20px;
  color: #007bff;
}

.loading-container p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* Error State */
.error-container {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-content {
  max-width: 400px;
  margin: 0 auto;
}

.error-content i {
  color: #dc3545;
  margin-bottom: 20px;
}

.error-content h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 24px;
}

.error-content p {
  color: #666;
  margin-bottom: 25px;
  font-size: 16px;
}

.retry-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Empty State */
.empty-container {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-content i {
  color: #6c757d;
  margin-bottom: 25px;
}

.empty-content h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 24px;
}

.empty-content p {
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.5;
}

.book-now-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.book-now-btn:hover {
  background: #218838;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

/* Bookings Section */
#bookings-section {
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Summary */
.bookings-summary {
  background: #f8f9fa;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-label {
  color: #666;
  font-size: 14px;
}

.summary-value {
  color: #333;
  font-weight: 600;
  font-size: 16px;
}

/* Bookings Grid */
.bookings-grid {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

/* Booking Card */
.booking-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.booking-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.booking-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #007bff, #0056b3);
}

/* Booking Header */
.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f1f3f4;
}

.booking-id {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.booking-id i {
  color: #007bff;
}

.booking-status {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-paid {
  background: #d4edda;
  color: #155724;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-failed {
  background: #f8d7da;
  color: #721c24;
}

.status-unknown {
  background: #e2e3e5;
  color: #383d41;
}

/* Travel Route */
.travel-route {
  margin-bottom: 15px;
}

.route-cities {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.city {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.city i {
  color: #007bff;
  font-size: 12px;
}

.city span {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.from-city {
  justify-content: flex-start;
}

.to-city {
  justify-content: flex-end;
}

.route-arrow {
  margin: 0 15px;
  color: #6c757d;
}

.trip-type {
  text-align: center;
}

.trip-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
}

/* Booking Details */
.booking-details {
  margin-bottom: 15px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.detail-item i {
  color: #6c757d;
  font-size: 12px;
  width: 12px;
}

.detail-label {
  color: #666;
  font-size: 12px;
  margin-right: 4px;
}

.detail-value {
  color: #333;
  font-weight: 500;
  font-size: 13px;
}

/* Traveler Info */
.traveler-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.traveler-name,
.traveler-phone {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #555;
}

.traveler-name i,
.traveler-phone i {
  color: #007bff;
  font-size: 12px;
}

/* Card Footer */
.card-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-top: 10px;
  border-top: 1px solid #f1f3f4;
}

.view-details {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #007bff;
  font-size: 13px;
  font-weight: 500;
}

.view-details i {
  font-size: 10px;
  transition: transform 0.3s ease;
}

.booking-card:hover .view-details i {
  transform: translateX(3px);
}

/* Load More Section */
.load-more-container {
  padding: 30px 20px;
  text-align: center;
  border-top: 1px solid #e9ecef;
}

.load-more-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.load-more-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* End of Results */
.end-of-results {
  padding: 20px;
  text-align: center;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.end-of-results p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-rides {
    padding: 15px;
  }

  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-content h2 {
    font-size: 24px;
  }

  .bookings-grid {
    grid-template-columns: 1fr;
    padding: 15px;
    gap: 15px;
  }

  .booking-card {
    padding: 15px;
  }

  .route-cities {
    flex-direction: column;
    gap: 10px;
  }

  .route-arrow {
    transform: rotate(90deg);
    margin: 5px 0;
  }

  .city {
    justify-content: center;
  }

  .detail-row {
    flex-direction: column;
    gap: 8px;
  }

  .traveler-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .bookings-summary {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .summary-item {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .user-rides {
    padding: 10px;
  }

  .header-content {
    padding: 15px;
  }

  .header-content h2 {
    font-size: 20px;
  }

  .refresh-btn {
    padding: 8px 16px;
    font-size: 12px;
  }

  .bookings-grid {
    padding: 10px;
    gap: 10px;
  }

  .booking-card {
    padding: 12px;
  }

  .booking-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .booking-id {
    font-size: 12px;
  }

  .city span {
    font-size: 12px;
  }

  .detail-label,
  .detail-value {
    font-size: 11px;
  }

  .traveler-name,
  .traveler-phone {
    font-size: 11px;
  }

  .load-more-btn {
    padding: 10px 20px;
    font-size: 12px;
  }
}

/* Animation for loading states */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.booking-card {
  animation: fadeIn 0.5s ease-out;
}

/* Hover effects for better UX */
.refresh-btn,
.retry-btn,
.book-now-btn,
.load-more-btn {
  position: relative;
  overflow: hidden;
}

.refresh-btn::before,
.retry-btn::before,
.book-now-btn::before,
.load-more-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.refresh-btn:hover::before,
.retry-btn:hover::before,
.book-now-btn:hover::before,
.load-more-btn:hover::before {
  left: 100%;
}