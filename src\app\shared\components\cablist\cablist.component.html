<div class="cabList">
<!--  <ul>
    <li *ngFor="let val of carCategoryFareList">
      <div class="cabType"><img src="{{imageFolderPath}}/{{val.attributes.carImage}}"></div>
      <div class="cabInfo">
        <div class="modalType">
          <span>{{ val.attributes.carCategoryName }} </span>
          &lt;!&ndash; <small> (Swift, i10 etc.)</small> &ndash;&gt;
          <div class="price"> Rs {{ val.attributes.fareAmount }} /KM</div>
        </div>
        <div class="bookingDetail">
          <span><i class="fa fa-user"></i> &nbsp; {{ val.attributes.numberOfPassesngerAllowed }} </span>
          <span><a class="fa fa-info-circle" title="Allow Baggages,Sanitized Cab,Professional Driver"></a></span>
          <div class="booking"></div>
        </div>
      </div>
    </li>

  </ul>-->
  <img src="../../../../assets/images/cabyaari-features-form.jpg" class="cabyaari-feature">
</div>
