import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PickDropDetailsComponent } from './pick-drop-details.component';

describe('PickDropDetailsComponent', () => {
  let component: PickDropDetailsComponent;
  let fixture: ComponentFixture<PickDropDetailsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PickDropDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PickDropDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
