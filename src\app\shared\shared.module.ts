// import {NgModule} from '@angular/core';

// import {CommonModule} from '@angular/common';
// import {ReactiveFormsModule} from '@angular/forms';
// import {RouterModule} from '@angular/router';
// import { SpinnerComponent } from './components/spinner/spinner.component';
// import { HeaderComponent } from './components/header/header.component';
// import { FooterComponent } from './components/footer/footer.component';
// import { SidemenuComponent } from './components/sidemenu/sidemenu.component';
// import { SearchOneWayComponent } from './components/search-one-way/search-one-way.component';
// import { SearchReturnComponent } from './components/search-return/search-return.component';
// import { SearchLocalComponent } from './components/search-local/search-local.component';
// import { SearchSharedComponent } from './components/search-shared/search-shared.component';
// import { CapitalizeFirstPipe } from './pipes/capitalize-first.pipe';
// import { CablistComponent } from './components/cablist/cablist.component';
// import { MostFavouriteRoutesComponent } from './components/most-favourite-routes/most-favourite-routes.component';
// import { OurServicesComponent } from './components/our-services/our-services.component';
// import { PromotionalBannersComponent } from './components/promotional-banners/promotional-banners.component';
// import { BecomeDriverComponent } from '../pages/become-driver/become-driver.component';
// import { AboutUsComponent } from '../pages/about-us/about-us.component';
// import { PrivacyAndPoliciesComponent } from '../pages/privacy-and-policies/privacy-and-policies.component';
// import { ContactUsComponent } from '../pages/contact-us/contact-us.component';
// import { FaqsComponent } from '../pages/faqs/faqs.component';


// @NgModule({
//   imports: [
//     CommonModule,
//     ReactiveFormsModule,
//     RouterModule
//   ],
//   declarations: [SpinnerComponent, HeaderComponent, FooterComponent,
//      SidemenuComponent, SearchOneWayComponent, SearchReturnComponent,
//       SearchLocalComponent, SearchSharedComponent, CapitalizeFirstPipe,
//       CablistComponent, MostFavouriteRoutesComponent, OurServicesComponent,
//       PromotionalBannersComponent, BecomeDriverComponent, AboutUsComponent,
//       PrivacyAndPoliciesComponent, ContactUsComponent, FaqsComponent],
//   exports: [
//     CommonModule
//   ]
// })

// export class SharedModule {
// }
