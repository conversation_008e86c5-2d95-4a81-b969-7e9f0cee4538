import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { AppConfig } from 'src/app/configs/app.config';
import { Observable, of } from 'rxjs';
import { FavouriteRoute } from '../models/favourite-route.model';
import { GenericAPIResponse, GenericAPIArrayResponse } from '../models/generic-api-response.model';
import { NearByCity } from '../models/city_model';
import { LoggerService } from '../interceptors/logger.service';

@Injectable({
  providedIn: 'root'
})
export class HomeApiService {

  private baseUrl: string;

  constructor(private httpClient: HttpClient) {
    this.logger = LoggerService.createLogger('HomeApiService');
    this.baseUrl = AppConfig.CabYaari_WebAPI_New;
  }

  private logger: LoggerService;

  getCityInfo = (pickUpCity: string, dropOffCity: string): Observable<GenericAPIArrayResponse<NearByCity>> => {
    this.logger.trace('getCityInfo() called with pickUpCity', pickUpCity, 'dropOffCity', dropOffCity);

    let queryParams: HttpParams = new HttpParams();
    queryParams = queryParams.append('pickUpCity', pickUpCity);
    queryParams = queryParams.append('dropOffCity',  dropOffCity);

    const url = this.baseUrl + '/api/v1/Home/GetCityInfo';
    return this.httpClient.get<GenericAPIArrayResponse<NearByCity>>(url, { params: queryParams });
  }

  getFavouriteRoutes = (): Observable<GenericAPIArrayResponse<FavouriteRoute>> => {
    const url = this.baseUrl + '/api/v1/Home/MostFavouriteRoutes';
    return this.httpClient.get<GenericAPIArrayResponse<FavouriteRoute>>(url);
  }

  getFavouriteRoutesDummy = (): Observable<GenericAPIArrayResponse<FavouriteRoute>> => {
    const jaipurDelhi: FavouriteRoute = new FavouriteRoute();
    jaipurDelhi.RouteName = 'Jaipur To Delhi';
    jaipurDelhi.Features = '4 Seats, 3 Bags, Ac, No Smoking';
    jaipurDelhi.Fare = '3,301';
    jaipurDelhi.CityImage = 'jaipur-to-delhi.jpg'

    const agraDelhi: FavouriteRoute = new FavouriteRoute();
    agraDelhi.RouteName = 'Agra To Delhi';
    agraDelhi.Features = '4 Seats, 3 Bags, Ac, No Smoking';
    agraDelhi.Fare = '2,986';
    agraDelhi.CityImage = 'agra-to-delhi.jpg'

    const chandigarhDelhi: FavouriteRoute = new FavouriteRoute();
    chandigarhDelhi.RouteName = 'Chandigarh to Delhi';
    chandigarhDelhi.Features = '4 Seats, 3 Bags, Ac, No Smoking';
    chandigarhDelhi.Fare = '3276';
    chandigarhDelhi.CityImage = 'chandigarh-to-delhi.jpg'

    // In grops of 3
    const routes: FavouriteRoute[] = [];
    routes.push(jaipurDelhi);
    routes.push(agraDelhi);
    routes.push(chandigarhDelhi);

    routes.push(agraDelhi);
    routes.push(chandigarhDelhi);
    routes.push(jaipurDelhi);
    
    routes.push(chandigarhDelhi);
    routes.push(jaipurDelhi);

    const response: GenericAPIArrayResponse<FavouriteRoute> = new GenericAPIResponse();
    response.succeeded = true;
    response.data = routes;

    return of(response);
  }
}
