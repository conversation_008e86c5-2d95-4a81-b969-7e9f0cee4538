import { Component, OnInit } from '@angular/core';
import { CompanyGenericService } from 'src/app/shared/services/company-generic.service';
import { AboutUs } from 'src/app/shared/models/about_model';
import { AboutService } from './services/about.service';
import { HttpErrorResponse } from '@angular/common/http';
import { AppConfig } from 'src/app/configs/app.config';

@Component({
  selector: 'app-about-us',
  templateUrl: './about-us.component.html',
  styleUrls: ['./about-us.component.css']
})
export class AboutUsComponent implements OnInit {

  constructor(private aboutService: AboutService) { }

  AboutUs: any;

  ngOnInit(): void {
    this.aboutUs();
  }
  imageFolderPath: string = AppConfig.imageFolderPath;

  private aboutUs(): void {

    this.aboutService.getAboutUs().subscribe(
          (response: AboutUs) => 
          {
            this.AboutUs = response[0];
            console.log(this.AboutUs);
         }, (error: HttpErrorResponse) => {
          console.log(error.error);
        });
  }
}
