export interface ETA {
    code: string;
    Server: string;
    routes: Route[];
    waypoints: Waypoint[];
}

export interface Route {
    legs: Leg[];
    weight_name: string;
    geometry: string;
    weight: number;
    distance: number;
    duration: number;
}

export interface Leg {
    steps: Step[];
    weight: number;
    distance: number;
    summary: number;
    duration: number;
}

export interface Step {
    intersections: Intersection[];
    driving_side: string;
    geometry: string;
    duration: number;
    distance: number;
    name: string;
    weight: number;
    mode: string;
    maneuver: Maneuver;
}

export interface Intersection {
    classes: string[];
    out: number;
    entry: boolean[];
    location: number[];
    bearings: number[];
}

export interface Maneuver {
    bearing_after: number;
    location: number[];
    type: string;
    bearing_before: number;
    modifier: string;
}

export interface Waypoint {
    hint: string;
    distance: number;
    location: number[];
    name: string;
}
