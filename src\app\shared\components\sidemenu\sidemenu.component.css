body {
  overflow-x: hidden;
}

.closebtn {
  font-size: 2em;
}

.toggle {
  position: absolute;
  left: 14px;
  top: 10px;
  font-size:20px;
  cursor:pointer;
  color:black;
}

a.list-group-item:hover {
  cursor: pointer;
}

a.list-group-item {
  padding: 8px 8px 8px 32px;
  color: #818181;
}

.sidenav {
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 2;
  top: 0;
  left: 0;
  background-color: #111;
  overflow-x: hidden;
  transition: 0.5s;
  padding-top: 60px;
}
  
.sidenav a {
  padding: 8px 8px 8px 32px;
  text-decoration: none;
  font-size: 16px;
  color: #818181;
  display: block;
  transition: 0.3s;
}
  
.sidenav a:hover {
  color: #f1f1f1;
}

.sidenav .closebtn {
  position: absolute;
  top: 0;
  right: 25px;
  font-size: 36px;
  margin-left: 50px;
}

@media screen and (max-height: 450px) {
  .sidenav {padding-top: 15px;}
  .sidenav a {font-size: 18px;}
}

/* ----- End of  Side Nam ------ */

#sidebar-wrapper {
  min-height: 100vh;
  position: fixed;
  z-index: 2;
  margin-left: -15rem;
  background-color:#111;
  -webkit-transition: margin .25s ease-out;
  -moz-transition: margin .25s ease-out;
  -o-transition: margin .25s ease-out;
  transition: margin .25s ease-out;
}

#sidebar-wrapper .sidebar-heading {
  padding: 0.875rem 1.25rem;
  font-size: 1.2rem;
}

#sidebar-wrapper .list-group {
  background-color: #111;
  width: 15rem;
}

#sidebar-wrapper .list-group-item {
  background-color: #111;
}

#wrapper.toggled #sidebar-wrapper {
  margin-left: 0;
}
