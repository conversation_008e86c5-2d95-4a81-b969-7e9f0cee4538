import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';
import { LoggerService } from './logger.service';

@Injectable()
export class UnauthorizedInterceptor implements HttpInterceptor {
  
  private logger: LoggerService;
  
  constructor(
    private authService: AuthService, 
    private router: Router) {
      this.logger = LoggerService.createLogger('UnauthorizedInterceptor');
    }

  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((err) => {
        if (err.status === 401) {
          this.authService.clearLocalStorage();
          
          // Get current URL to redirect back after login
          const currentUrl = this.router.url;
          console.log('Redirecting to login with returnUrl:', currentUrl);
          
          this.router.navigate(['login'], {
            queryParams: { returnUrl: currentUrl }
          });
        }

        if (!environment.production) {
          this.logger.error(err);
        }
        const error = (err && err.error && err.error.message) || err.statusText;
        return throwError(error);
      })
    );
  }
}