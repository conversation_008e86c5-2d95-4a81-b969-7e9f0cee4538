#taxi-div{
    background-image: url('../../../../assets/images/top-bg.png');
    background-size: 100% 100%;
}

#our-services-div {
    padding: 1rem 1rem 0 1rem;
}

.callus {
    z-index: 1;
}

.callus span {
    color: #ffed4d;
    padding: 5px 6px;
    font-weight: bold;
    background: #000;
    height: 30px;
}

.callus span:hover {
    color:#000;
    background-color: #fff;
    cursor: pointer;
}

.callus i {
    background: #ffed4d;
    display: inline-block;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    color: #201e1e;
}

/* Adjust for desktop width */
@media only screen and (min-width: 992px) {
    #our-services-div {
        padding: 4rem 4rem 0 4rem;
    }

    .callus {
        padding: 0;
        margin-left: 41rem;
    }
}

#offer-heading {
    font-size: 16px;
    color: #3a3a3a;
    font-weight: 700;
}

p {
    font-size: 13px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

header.major h2 {
    font-size: 3em;
    font-weight: bold;
    color: #000;
    margin: 0px;
    font-family: 'Segoe UI',    Tahoma, Geneva, Verdana, sans-serif;
}

h2 {
    font-size: 1.5em;
    line-height: 1.5em;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

header.major span {
    font-size: 24px;
    color: #4a4a4a;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}