
.toggle {
  position: absolute;
  left: 14px;
  top: 10px;
  font-size:20px;
  cursor:pointer;
  color:black;
}

.disabled {
  cursor: not-allowed;
  opacity: 30%;
}

.bookBtn{
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.enquire-now {
  border-radius: 35px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
  background: grey; /* WhatsApp green color */
  padding: 0.5em;
  text-align: center;
  color: #fff;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s ease;

  width: 8rem;
  margin-left: 26rem;
}

.enquire-now:hover {
  background: darkgrey; /* Darker WhatsApp green on hover */
}

.bookingDetail  {
  padding: 1em 0px;
  width: 47%;
  display: inline-block;
  font-size: 16px;
  text-align: center;
  color: #2b1b1b;
}

  /*---- Pic/DropOff----*/
.smallheader .topheader {
    min-height: 80px;
    padding: 1em;
}

#header.smallheader .forminner {margin-top: -20px;}
#header.smallheader .forminner#routeDetails {margin-top: 5px;}

.head a {
    position: relative;
    top: 2px;
    right: -10px;
    color: #000;
}

.cabList li:hover {
  cursor: pointer;
  opacity: 0.5;
}

.highlighted {
  background-color: #1abc9c;
}

.smallheader .forminner .form-group {height: auto;}
.smallheader .forminner .form-group i{
    font-size: 12px;
    position: relative;
    top: -1px;
    right: 20px;
    color: #000;
}

#routeDiv {
  display: flex;
  justify-content: space-between;
}

.route-box {
  background: #ffffff;
  width: 48%;
  cursor: pointer;
  border-radius: 4px;
  display: block;
  position: relative;
}

.route-label {
  display: flex;
  border: 1px solid #f3f3f3;
  border-radius: 4px;
}

.route-info {
  display: flex;
  flex-flow: column;
  overflow: hidden;
  width: 100%;
}

.route-field {
  margin: 0 5%;
  padding: 0 5%;
  letter-spacing: calc(1.6px / 3);
  color: #ffb100;
  font-weight: bold;
}

.route-metric {
  margin: 0 5%;
  padding: 0 5%;
  font-size: 12px;
  position: relative;
  color: #c4c4c4;
}

.smallheader .forminner .form-group input[type="email"],
.smallheader .forminner .form-group input[type="date"],
.smallheader .forminner .form-group input[type="time"]
{
    font-size: 14px;
    width: calc(100% - 30px);
    display: inline-block;
    margin-left: 12px;
    border: none;
    border-bottom: 1px solid #efefef;
    border-radius: 0px;
    padding: 0px;
}

.mappage{
  height: 100vh;
}

#map {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

.toggle a{
    color: #585858;
}


.month {
    width: 100%;
    background: #1abc9c;
    text-align: center;
  }

  .month ul {
    margin: 0;
    padding: 0;
  }

  .month ul li {
    color: white;
    font-size: 20px;
    text-transform: uppercase;
    letter-spacing: 3px;
  }

  .month .prev {
    float: left;
    padding-top: 10px;
  }

  .month .next {
    float: right;
    padding-top: 10px;
  }

  .weekdays {
    margin: 0;
    padding: 10px 0;
    background-color: #ddd;
  }

  .weekdays li {
    display: inline-block;
    width: 13.6%;
    color: #666;
    text-align: center;
  }

  .days {
    padding: 10px 0;
    background: #eee;
    margin: 0;
  }

  .days li {
    list-style-type: none;
    display: inline-block;
    width: 13.6%;
    text-align: center;
    margin-bottom: 5px;
    font-size:12px;
    color: #777;
  }

  .days li .active {
    padding: 5px;
    background: #1abc9c;
    color: white !important
  }

  /* Add media queries for smaller screens */
  @media screen and (max-width:720px) {
    .weekdays li, .days li {width: 13.1%;}
  }

  @media screen and (max-width: 420px) {
    .weekdays li, .days li {width: 12.5%;}
    .days li .active {padding: 2px;}
  }

  @media screen and (max-width: 290px) {
    .weekdays li, .days li {width: 12.2%;}
  }
/*---- End Pic/DropOff----*/

/* Override any conflicting jQuery UI styles with higher specificity */
:host ::ng-deep .ui-widget {
  font-family: 'Roboto', sans-serif !important;
}

:host ::ng-deep .ui-widget-content {
  border: none !important;
  background: #fff !important;
  color: #546E7A !important;
}

/* Force override jQuery UI datepicker styles */
:host ::ng-deep .ui-datepicker.ui-widget.ui-widget-content {
  background-color: #fff !important;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1) !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem !important;
  border: none !important;
  font-family: 'Roboto', sans-serif !important;
  width: auto !important;
  display: block !important;
}

/* Custom Date Picker Styles */
.custom-datepicker-wrapper {
  position: relative;
  margin-left: 12px;
}

.custom-datepicker-wrapper label {
  font-size: 0.75rem;
  font-weight: 400;
  display: block;
  margin-bottom: 0.5rem;
  color: #B0BEC5;
  position: relative;
  padding: 0.5rem 0rem;
  border-bottom: 1px solid #efefef;
}

.custom-datepicker {
  font-family: 'Roboto', sans-serif;
  display: block;
  border: none;
  border-radius: 0.25rem;
  line-height: 1.5rem;
  padding: 0;
  font-size: 14px !important;
  color: #000;
  width: 100%;
  margin-top: 0;
  background: transparent;
}

.custom-datepicker:focus {
  outline: none;
}

:host ::ng-deep .ui-datepicker {
  background-color: #fff !important;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1) !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem !important;
  border: none !important;
  font-family: 'Roboto', sans-serif !important;
  width: auto !important;
}

:host ::ng-deep .ui-datepicker table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  width: 100% !important;
  margin: 0 !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-calendar thead th {
  padding: 0.25rem 0 !important;
  text-align: center !important;
  font-size: 0.75rem !important;
  font-weight: 400 !important;
  color: #78909C !important;
  background: none !important;
  border: none !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-calendar tbody td {
  width: 2.5rem !important;
  text-align: center !important;
  padding: 0 !important;
  border: none !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-calendar tbody td a {
  display: block !important;
  border-radius: 0.25rem !important;
  line-height: 2rem !important;
  transition: 0.3s all !important;
  color: #546E7A !important;
  font-size: 0.875rem !important;
  text-decoration: none !important;
  border: none !important;
  background: none !important;
  width: 100% !important;
  height: 2rem !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-calendar tbody td a:hover {
  background-color: #E0F2F1 !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-calendar tbody td a.ui-state-active,
:host ::ng-deep .ui-datepicker .ui-datepicker-calendar tbody td a.ui-state-highlight {
  background-color: #009688 !important;
  color: white !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-header {
  position: relative !important;
  text-align: center !important;
  margin-bottom: 0.5rem !important;
  background: none !important;
  border: none !important;
  padding: 0.5rem 0 !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-header a {
  cursor: pointer !important;
  position: absolute !important;
  top: 0 !important;
  width: 2rem !important;
  height: 2rem !important;
  margin: 0.5rem !important;
  border-radius: 0.25rem !important;
  transition: 0.3s all !important;
  text-indent: -9999px !important;
  border: none !important;
  background-color: transparent !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-header a:hover {
  background-color: #ECEFF1 !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-header .ui-datepicker-prev {
  left: 0 !important;
  background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMyIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDEzIDEzIj48cGF0aCBmaWxsPSIjNDI0NzcwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03LjI4OCA2LjI5NkwzLjIwMiAyLjIxYS43MS43MSAwIDAgMSAuMDA3LS45OTljLjI4LS4yOC43MjUtLjI4Ljk5OS0uMDA3TDguODAzIDUuOGEuNjk1LjY5NSAwIDAgMSAuMjAyLjQ5Ni42OTUuNjk1IDAgMCAxLS4yMDIuNDk3bC00LjU5NSA0LjU5NWEuNzA0LjcwNCAwIDAgMS0xLS4wMDcuNzEuNzEgMCAwIDEtLjAwNi0uOTk5bDQuMDg2LTQuMDg2eiIvPjwvc3ZnPg==") !important;
  background-repeat: no-repeat !important;
  background-size: 0.5rem !important;
  background-position: 50% !important;
  transform: rotate(180deg) !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-header .ui-datepicker-next {
  right: 0 !important;
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMyIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDEzIDEzIj48cGF0aCBmaWxsPSIjNDI0NzcwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03LjI4OCA2LjI5NkwzLjIwMiAyLjIxYS43MS43MSAwIDAgMSAuMDA3LS45OTljLjI4LS4yOC43MjUtLjI4Ljk5OS0uMDA3TDguODAzIDUuOGEuNjk1LjY5NSAwIDAgMSAuMjAyLjQ5Ni42OTUuNjk1IDAgMCAxLS4yMDIuNDk3bC00LjU5NSA0LjU5NWEuNzA0LjcwNCAwIDAgMS0xLS4wMDcuNzEuNzEgMCAwIDEtLjAwNi0uOTk5bDQuMDg2LTQuMDg2eiIvPjwvc3ZnPg==') !important;
  background-repeat: no-repeat !important;
  background-size: 10px !important;
  background-position: 50% !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-header a span {
  display: none !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-title {
  text-align: center !important;
  line-height: 2rem !important;
  margin-bottom: 0.25rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  padding-bottom: 0.25rem !important;
  color: #546E7A !important;
}

:host ::ng-deep .ui-datepicker .ui-datepicker-week-col {
  color: #78909C !important;
  font-weight: 400 !important;
  font-size: 0.75rem !important;
}

.icon-calendar {
  position: absolute;
  top: 12px;
  right: 0;
  cursor: pointer;
}

.icon-calendar img {
  max-width: 22px;
}

/* Custom Time Picker Styles */
.custom-timepicker-wrapper {
  position: relative;
  margin-left: 12px;
}

.icon-timepicker {
  position: absolute;
  top: 7px;
  right: 0;
  cursor: pointer;
}

.custom-timepicker {
  border: 0;
  outline: 0;
  background: transparent;
  font-family: 'Roboto', sans-serif;
  display: block;
  border: none;
  border-radius: 0.25rem;
  line-height: 1.5rem;
  padding: 0;
  font-size: 14px !important;
  color: #000;
  width: 100%;
  margin-top: 0;
  cursor: pointer;
}

.icon-timepicker img {
  max-width: 22px;
}

:host ::ng-deep .time-picker {
  position: absolute !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 10px !important;
  background: #eeeeee !important;
  border-radius: 6px !important;
  margin-top: 10px !important;
  z-index: 1000 !important;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #ddd !important;
  min-width: 200px !important;
  white-space: nowrap !important;
}

:host ::ng-deep .time-picker__select {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  outline: none !important;
  text-align: center !important;
  border: 1px solid #dddddd !important;
  border-radius: 6px !important;
  padding: 6px 10px !important;
  background: #ffffff !important;
  cursor: pointer !important;
  font-family: 'Roboto', sans-serif !important;
  margin: 0 2px !important;
  font-size: 14px !important;
  display: inline-block !important;
  vertical-align: middle !important;
}

/* Additional high-specificity overrides for jQuery UI datepicker */
:host ::ng-deep div.ui-datepicker.ui-widget.ui-widget-content.ui-helper-clearfix.ui-corner-all {
  background-color: #fff !important;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1) !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem !important;
  border: none !important;
  font-family: 'Roboto', sans-serif !important;
  width: auto !important;
  min-width: 250px !important;
}

/* Override jQuery UI default width */
:host ::ng-deep .ui-datepicker.ui-widget {
  width: auto !important;
  min-width: 250px !important;
}

/* Ensure proper display */
:host ::ng-deep .ui-datepicker {
  display: block !important;
}
