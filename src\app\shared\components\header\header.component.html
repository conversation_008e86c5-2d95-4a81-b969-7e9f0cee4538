
<app-sidemenu></app-sidemenu> 
<header id="header">


  <span class="toggle menu-toggle"> <img src="{{imageFolderPath}}/toggle-icon.png"> </span>
  <!-- <h3  style="color: brown; float: right;  margin-right:8px;">Call Us :+91-7414000436</h3> -->
  <br>
  <div class="topheader"><a routerLink="/home" class="logo"><img src="{{imageFolderPath}}/logo.png" alt="" style="height: 48px; width: 175px;" /></a>
    <div class="bookType">
      <a class="active">Oneway</a>
    </div>
  </div>
  <div class="forminner">
    <div class="head"><span></span>Get dropped from one city to another</div>
    <form>
      <div class="form-group">
        <label for="PickupCity">From</label>

        <input type="text" placeholder="Pick up City" class="form-control" matInput [formControl]="pickUpCityControl"
          [matAutocomplete]="pickupCity">
        <mat-autocomplete #pickupCity="matAutocomplete" (optionSelected)="_filterPickUpCityLatLong($event)">
          <mat-option *ngFor="let pickupCities of filteredOptionsPickUpCity | async" [value]="pickupCities">
            {{pickupCities}}
          </mat-option>
        </mat-autocomplete>

      </div>

      <div class="line"><a href="#" (click)="InputSwipe()"><img src="{{imageFolderPath}}/swap.png" class=""></a></div>

      <div class="form-group">
        <label for="DropOffCity">To</label>


        <input type="text" placeholder="DropOff City" class="form-control" matInput [formControl]="dropOffCityControl"
          [matAutocomplete]="dropOffCity" >
        <mat-autocomplete #dropOffCity="matAutocomplete" (optionSelected)="_filterDropOffCityLatLong($event)">
          <mat-option *ngFor="let dropOffCities of filteredOptionsDropOffCity | async" [value]="dropOffCities">
            {{dropOffCities}}
          </mat-option>
        </mat-autocomplete>
      </div>
        </form>


  </div>
  <div class="next-btn"><a (click)="ValidateInputs()">Next <i class="fa fa-arrow-circle-right"></i></a></div>
  

  <app-cablist [imageFolderPath]="imageFolderPath"></app-cablist>
</header>
