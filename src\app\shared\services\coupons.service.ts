import { catchError } from 'rxjs/operators';
import { AppConfig } from 'src/app/configs/app.config';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { throwError } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CouponsService {

  private baseUrl: string;

  constructor(private httpClient: HttpClient) { 
    this.baseUrl = AppConfig.CabYaari_WebAPI_New;
  }

applyCoupon = (couponCode: string) => {
  const url = this.baseUrl + '/api/v1/Home/Discount';
  return this.httpClient.get(url+'?couponCode='+couponCode.toUpperCase()).pipe(catchError(this.handleError));
}

handleError = (error: HttpErrorResponse) => {
  return throwError(error);
}

validateCoupon = (response: string, amount: number) => {
  if(amount<response['minimumFareAmount']){
    return '-1';
  }
  else{
    var discount = (response['discount']/100) * amount;
    if(discount>response['maximumDiscount'])
    return response['maximumDiscount']; 
    else
    return discount;
  }


}

}
