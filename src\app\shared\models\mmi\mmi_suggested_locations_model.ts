

    export class AddressTokens {
    }

    export class RichInfo {
    }

    export class SuggestedLocation {
        type: string;
        typeX: number;
        placeAddress: string;
        latitude: number;
        longitude: number;
        eLoc: string;
        entryLatitude: number;
        entryLongitude: number;
        placeName: string;
        alternateName: string;
        keywords: string[];
        addressTokens: AddressTokens;
        p: number;
        orderIndex: number;
        score: number;
        suggester: string;
        richInfo: RichInfo;
    }

    export interface UserAddedLocation {
        eLoc: string;
        entryLatitude: number;
        entryLongitude: number;
        latitude: number;
        longitude: number;
        orderIndex: number;
        placeAddress: string;
        placeName: string;
        resultType: string;
        type: string;
        userName: string;
    }

    export class MMISuggestedModel {
        suggestedLocations: SuggestedLocation[];
        userAddedLocations: UserAddedLocation[];
        suggestedSearches: any[];
    }



