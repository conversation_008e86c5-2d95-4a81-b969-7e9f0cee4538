{"name": "cab-yaari-user-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^9.1.12", "@angular/cdk": "^9.2.4", "@angular/common": "~9.1.0", "@angular/compiler": "~9.1.0", "@angular/core": "~9.1.0", "@angular/forms": "~9.1.0", "@angular/localize": "~9.1.0", "@angular/material": "^9.2.4", "@angular/platform-browser": "~9.1.0", "@angular/platform-browser-dynamic": "~9.1.0", "@angular/router": "~9.1.0", "@ng-bootstrap/ng-bootstrap": "^6.2.0", "@types/jquery": "^3.5.0", "@types/jqueryui": "^1.12.24", "axios": "^0.21.4", "bootstrap": "^4.5.0", "jquery": "^3.5.1", "jquery-ui": "^1.14.1", "ngx-toastr": "^13.0.0", "popper.js": "^1.16.1", "rxjs": "~6.5.4", "tslib": "^1.10.0", "zone.js": "~0.10.2"}, "devDependencies": {"@angular-devkit/build-angular": "~0.901.0", "@angular/cli": "~9.1.0", "@angular/compiler-cli": "~9.1.0", "@angular/language-service": "~9.1.0", "@types/google.maps": "^3.58.1", "@types/jasmine": "~3.5.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "codelyzer": "^5.1.2", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~2.1.0", "karma-jasmine": "~3.0.1", "karma-jasmine-html-reporter": "^1.4.2", "protractor": "~5.4.3", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~3.8.3"}}