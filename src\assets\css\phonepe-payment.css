/* PhonePe Payment Modal Styles */
#phonepe-payment-modal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  z-index: 10000 !important;
}

#phonepe-modal-content {
  background-color: white !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
  position: relative !important;
  width: 95% !important;
  max-width: 450px !important;
  height: 85vh !important;
  max-height: 700px !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

#phonepe-iframe-container {
  flex: 1 !important;
  padding: 0 !important;
  overflow: hidden !important;
  background-color: white !important;
}

/* PhonePe iframe specific styles */
#phonepe-iframe-container iframe {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 0 0 12px 12px !important;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  #phonepe-modal-content {
    width: 98% !important;
    height: 90vh !important;
    max-height: none !important;
  }
}

@media (max-width: 480px) {
  #phonepe-modal-content {
    width: 100% !important;
    height: 100vh !important;
    border-radius: 0 !important;
  }
  
  #phonepe-iframe-container iframe {
    border-radius: 0 !important;
  }
}

/* Loading spinner for PhonePe */
.phonepe-loading {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  height: 200px !important;
  flex-direction: column !important;
}

.phonepe-loading-spinner {
  border: 4px solid #f3f3f3 !important;
  border-top: 4px solid #3498db !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  animation: phonepe-spin 1s linear infinite !important;
  margin-bottom: 15px !important;
}

@keyframes phonepe-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.phonepe-loading-text {
  color: #666 !important;
  font-size: 14px !important;
  text-align: center !important;
}

/* Ensure PhonePe modal appears above everything */
body.phonepe-modal-open {
  overflow: hidden !important;
}

/* Fix for any conflicting styles */
#phonepe-payment-modal * {
  box-sizing: border-box !important;
}

/* PhonePe specific iframe container adjustments */
.phonepe-checkout-container {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  background: white !important;
}

/* Error state styles */
.phonepe-error {
  padding: 20px !important;
  text-align: center !important;
  color: #dc3545 !important;
}

.phonepe-error-icon {
  font-size: 48px !important;
  margin-bottom: 15px !important;
}

.phonepe-error-message {
  font-size: 16px !important;
  margin-bottom: 20px !important;
}

.phonepe-retry-button {
  background-color: #007bff !important;
  color: white !important;
  border: none !important;
  padding: 10px 20px !important;
  border-radius: 5px !important;
  cursor: pointer !important;
  font-size: 14px !important;
}

.phonepe-retry-button:hover {
  background-color: #0056b3 !important;
}
