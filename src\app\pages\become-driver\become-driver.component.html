<app-header [imageFolderPath]="imageFolderPath" ></app-header> 


<!-- Main -->
<div id="main" class="driverform">
				
    <!-- Three -->
    <section id="two">
        <div class="row">
            <div class="col-12 col-sm-8">
                <h2>Become a Driver</h2>
                <p> CabYaari.com providing business opportunities for car driver ,car vendor so they can earn more profit in their business.</p>        
            </div>
        </div>

        <div class="row">
            <div class="col-12 col-12-small whitebox">
                <div class="form">
                    <div class="row gtr-uniform gtr-50">
                        <div class="col-6"><label>First Name</label><input type="text" name="firstname" [(ngModel)]="becomeDriver.driverFirtsName"  /></div>
                        <div class="col-6"><label>Last Name</label><input type="text" name="lastname" [(ngModel)]="becomeDriver.driverLastName" /></div>
                        <div class="col-12"><label>Email</label><input type="email" name="email" [(ngModel)]="becomeDriver.driverEmailId" /></div>
                        <div class="col-12 col-sm-6"><label>Mobile Number</label><input type="text" name="phone" [(ngModel)]="becomeDriver.driverPhoneNumber" /></div>
                        <div class="col-12 col-sm-6">
                            <label>Is it your What's App Number? </label>
                            <label class="switch">
                                <input type="checkbox" [(ngModel)]="becomeDriver.isWhatsAppNumber" >
                                <span class="slider round">Yes &nbsp; No</span>
                              </label>
                        </div>
                        <div class="col-12"><label>Enter Your Address</label><textarea name="message" id="message" rows="2" [(ngModel)]="becomeDriver.driverAddress" ></textarea></div>
                    </div>
                </div>
                <ul class="actions">
                    <li><input type="submit" value="Submit" class="submit"  (click)="BecomeDriverRequest()"/></li>
                </ul>
            </div>
            
        <div class="col-lg-12 d-none d-lg-block" style="height: 300px;">

        </div>
        </div>
    </section>
    
    

        
    
</div>