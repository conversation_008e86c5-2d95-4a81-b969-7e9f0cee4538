import { Injectable } from '@angular/core';
import { LocationCoordinate } from '../models/location_coordinate_model';
import { LoggerService } from '../interceptors/logger.service';

/**
 * Refer https://gist.github.com/ismaels/6636986
 */
@Injectable({
    providedIn: 'root'
})
export class PolyLineDecoderService {

    constructor() {
        this.logger = LoggerService.createLogger('PolyLineDecoderService');
    }

    private logger: LoggerService;

    decode = (encodedPolyline: string): LocationCoordinate[] => {
        this.logger.trace('decode() called with encodedPolyline', encodedPolyline);
        let points: LocationCoordinate[] = []
        let index = 0, len = encodedPolyline.length;
        let lat = 0, lng = 0;
        while (index < len) {
            let b, shift = 0, result = 0;
            do {

                b = encodedPolyline.charAt(index++).charCodeAt(0) - 63;//finds ascii                                                                                    //and substract it by 63
                result |= (b & 0x1f) << shift;
                shift += 5;
            } while (b >= 0x20);


            let dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
            lat += dlat;
            shift = 0;
            result = 0;
            do {
                b = encodedPolyline.charAt(index++).charCodeAt(0) - 63;
                result |= (b & 0x1f) << shift;
                shift += 5;
            } while (b >= 0x20);
            let dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
            lng += dlng;

            const location = new LocationCoordinate();
            location.latitude = (lat / 1E5);
            location.longitude = (lng / 1E5);

            points.push(location);
        }

        this.logger.trace('decode() returnig points', points);
        return points;  
    }
}
