import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable } from 'rxjs';
import { MmiService } from '../../services/mmi/mmi-service.service';

@Injectable({
  providedIn: 'root'
})
export class MmiInterceptor implements HttpInterceptor {

  constructor(private authenticationService: MmiService) { }

    intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
        // add authorization header with jwt token if available
      // let currentToken = '//this.authenticationService.currentTokenValue';
       let currentToken = '45eea35f-2dd5-47e9-8f03-cec48a1411ad';
       //if (currentToken && currentToken.access_token) 
       if (currentToken !== null) 
       {
            request = request.clone({
                setHeaders: { Authorization: `bearer 45eea35f-2dd5-47e9-8f03-cec48a1411ad`
                }
            });
        }
        return next.handle(request);
    }
}

