.trigger-btn {
	display: inline-block;
	margin: 100px auto;
}

.toggle{
    position: absolute;
    left: 14px;
    top: 10px;
    font-size:20px;
    cursor:pointer;
    color:black;
}

.toggle a{
    color: #585858;
}


.smallheader .topheader {
    min-height: 80px;
    padding: 1em;
}

#header.smallheader .forminner {
    margin-top: -20px;
}

#header.smallheader .forminner#routeDetails {
    margin-top: 5px;
}

.head a {
    position: relative;
    top: 2px;
    right: -10px;
    color: #000;
}

.smallheader .forminner .form-group {
    height: auto;
}

.smallheader .forminner .form-group i {
    font-size: 12px;
    position: relative;
    top: -1px;
    right: 20px;
    color: #000;
}

.smallheader .forminner .form-group input[type="email"],
.smallheader .forminner .form-group input[type="date"],
.smallheader .forminner .form-group input[type="time"] {
    font-size: 14px;
    width: calc(100% - 30px);
    display: inline-block;
    margin-left: 12px;
    border: none;
    border-bottom: 1px solid #efefef;
    border-radius: 0px;
    padding: 0px;
}

#main > section:first-child {
    border-top: 0;
    margin-top: 0;
    padding-top: 0;
}

.bookingDetail  {
    padding: 1em 0px;
    width: 47%;
    display: inline-block;
    font-size: 16px;
    text-align: center;
    color: #2b1b1b;
}