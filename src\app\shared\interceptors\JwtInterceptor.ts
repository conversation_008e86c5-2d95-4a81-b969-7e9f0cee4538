import { <PERSON>ttp<PERSON>e<PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttpEvent, HttpInterceptor } from '@angular/common/http';
import { AuthService } from '../services/auth.service';
import { Observable } from 'rxjs';
import { AppConfig } from 'src/app/configs/app.config';
import { Injectable } from '@angular/core';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {

  webAPIEndPoint :string;

    constructor(private authService: AuthService) {
      this.webAPIEndPoint = AppConfig.CabYaari_WebAPI;
    }
  
    intercept(
      request: HttpRequest<unknown>,
      next: <PERSON>ttp<PERSON>and<PERSON>
    ): Observable<HttpEvent<unknown>> {
      // add JWT auth header if a user is logged in for API requests
      const accessToken = localStorage.getItem('access_token');
      const isApiUrl = request.url.startsWith(this.webAPIEndPoint);
      if (accessToken && isApiUrl) {
        request = request.clone({
          setHeaders: { Authorization: `Bear<PERSON> ${accessToken}` },
        });
      }
  
      return next.handle(request);
    }
  }