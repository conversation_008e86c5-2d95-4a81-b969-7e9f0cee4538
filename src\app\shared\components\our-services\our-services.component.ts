import { Component, OnInit, Input, ViewChild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CompanyGenericService } from '../../services/company-generic.service';
import { Service } from '../../models/service_model';
import { HttpErrorResponse } from '@angular/common/http';
import { SpinnerComponent } from '../spinner/spinner.component';
import { AppConstants } from '../../constants/AppConstants';
import { SCREEN_SIZE } from '../../constants/screen-size.enum';
import { LoggerService } from '../../interceptors/logger.service';
import { ResizeService } from '../../services/resize.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-our-services',
  templateUrl: './our-services.component.html',
  styleUrls: ['./our-services.component.css']
})
export class OurServicesComponent implements OnInit, OnD<PERSON>roy {
  constructor(
    private companyGenericService: CompanyGenericService,
    private resizeService: ResizeService) { 
    this.logger = LoggerService.createLogger('OurServicesComponent');
  }

  private logger: LoggerService;

  @Input() imageFolderPath: string;

  services: Array<Service>;
  groupedServices: Array<Array<Service>>;

  @ViewChild('spinner', { static: true })
  private spinner: SpinnerComponent;

  private currentScreenSize: SCREEN_SIZE = SCREEN_SIZE.LG;

  private resizeSubscription: Subscription;

  ngOnInit() {
    this.initFields();
    this.getServices();
    this.subscribeResizeService();
  }

  ngOnDestroy() {
    if (this.resizeSubscription) {
      this.resizeSubscription.unsubscribe();
    }
  }

  private initFields = () => {
    this.services = new Array();
    this.groupedServices = new Array();
  }

  private subscribeResizeService = () => {
    this.logger.trace('subscribeResizeService() called');
    this.resizeSubscription = this.resizeService.onResize().subscribe(
      (size: SCREEN_SIZE) => {
        this.logger.trace('listened to size in resizeService', size);
        this.currentScreenSize = size;
        this.logger.trace('currentScreenSize', this.currentScreenSize);

        this.logger.trace('services', this.services);
        if (this.services) {
          this.groupServices();
        }
      }
    );
  }

  private getServices() {
    this.showSpinner();
    this.companyGenericService.getServices().subscribe(
      (response: Array<Service>) => {
        this.hideSpinner();
        this.addServices(response);
        this.groupServices();
      }, 
      (error: HttpErrorResponse) => {
        this.hideSpinner();
        this.logger.error('getServices() error', error.error);
      }
    );
  }

  private addServices = (response: Array<Service>) => {
    response.forEach(result => this.services.push(result));
  }

  private groupServices = () => {    
    let favouriteRoutesTemp: Array<Service> = new Array();
    this.groupedServices = new Array();
    let groupCounter = 0;
    const groupSize = this.getGroupSize();

    this.services.forEach(result => {
      if (groupCounter === groupSize) {
        groupCounter = 0;
        this.groupedServices.push(favouriteRoutesTemp);
        favouriteRoutesTemp = new Array();
      }

      favouriteRoutesTemp.push(result);

      groupCounter++;
    });

    if (favouriteRoutesTemp.length > 0) {
      this.groupedServices.push(favouriteRoutesTemp);
    }
  }

  private getGroupSize = (): number => {
    this.logger.trace('getGroupSize() called');
    this.logger.trace('currentScreenSize', this.currentScreenSize);

    const result = (this.currentScreenSize === SCREEN_SIZE.XS) ? 1 : AppConstants.FAVOURITE_ROUTE_GROUP_SIZE;
    this.logger.trace('getGroupSize() returning result', result);
    return result;
  }

  private hideSpinner = () => {
    this.spinner.hide();
  }

  private showSpinner = () => {
    this.spinner.show();
  }
}
