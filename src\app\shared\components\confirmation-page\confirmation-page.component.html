<!-- Main -->
<div id="main" class="user-rides">
    
  <!-- Three -->
  <section id="two">
    <h2><strong>
      <svg style="color: green;" width="2em" height="2em" viewBox="0 0 16 16" class="bi bi-check" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" d="M10.97 4.97a.75.75 0 0 1 1.071 1.05l-3.992 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.236.236 0 0 1 .02-.022z"/>
    </svg>
    <span id="confirm">Thanks for Booking with us.</span></strong></h2>
    <h3><span class="thanksquote">Your Booking ID is <strong style="font-weight: 900;">{{ bookingId }}</strong>. Please refer to the mail for more details.<br/><br/></span></h3>

    <!-- Loading indicator -->
    <div *ngIf="isLoading" class="text-center">
      <div class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div>
      <p>Loading booking details...</p>
    </div>

    <div class="row" *ngIf="!isLoading">
      <div class="col-12 col-md-6">
                      <div class="mapfield">
                         
                              <div href="#" class="request">
                                  <div class="travel-detail">
                                      <div class="city">{{ bookingData?.pickUpCity || bookingRequest?.pickUpCity }} <span class="times">{{ bookingData?.pickUpTime || bookingRequest?.pickUpTime }}</span></div>
                                      <div class="city">{{ bookingData?.dropOffCity || bookingRequest?.dropOffCity }} <span class="times">{{ calculateDropOffTime() }}</span></div>
                                  </div>

                                  <div class="booking-detail">

                                    <div class="timing"> {{ bookingData?.pickUpAddress || bookingRequest?.pickUpAddress }}</div>
                                    <div class="timing">  {{ bookingData?.dropOffAddress || bookingRequest?.dropOffAddress }}</div>
                                  </div>
                                </div>

                                <!-- Trip Details -->
                                <div class="trip-details mt-3">
                                  <h4>Trip Details</h4>
                                  <div class="detail-row">
                                    <span class="label">Trip Type:</span>
                                    <span class="value">{{ bookingData?.tripType || bookingRequest?.tripType }}</span>
                                  </div>
                                  <div class="detail-row">
                                    <span class="label">Distance:</span>
                                    <span class="value">{{ bookingData?.distance || bookingRequest?.distance }} KM</span>
                                  </div>
                                  <div class="detail-row">
                                    <span class="label">Duration:</span>
                                    <span class="value">{{ bookingData?.duration || bookingRequest?.duration }}</span>
                                  </div>
                                  <div class="detail-row">
                                    <span class="label">Car Category:</span>
                                    <span class="value">{{ bookingData?.carCategory || bookingRequest?.carCategory }}</span>
                                  </div>

                                  <div class="detail-row">
                                    <span class="label">Capacity:</span>
                                    <span class="value">{{ bookingData?.carCapacity || bookingRequest?.carCapacity }} passengers</span>
                                  </div>
                                  <div class="detail-row">
                                    <span class="label">Pickup Date:</span>
                                    <span class="value">{{ bookingData?.pickUpDate || bookingRequest?.pickUpDate }}</span>
                                  </div>
                                </div>

                                <div class="map">
                                  <div id="confirmation-map" style="height: 100%; width: 100%;"></div>
                                </div>
                                

                                <!--<div class="driver-detail">
                                  <span class="rating">2.3 <i class="fa fa fa-star"></i></span>
                                  <div class="user-image">
                                    <img src="{{imageFolderPath}}/driver.jpg">
                                  </div>
                                  <div class="bigheading">Mukesh Ambani
                                    <span class="smallheading">{{ bookingRequest?.carCategory }}</span>
                                    <a href="#" class="report">Report Issue</a>
                                  </div>
      
                                  <div class="bigheading">{{ bookingRequest?.distance }}
                                    <span class="smallheading">kilometers</span>
                                  </div>
      
                                  
                                </div>-->
                              
                      </div>
                      
                      

                  </div>
                  
                  <div class="col-12 col-md-6">
                    <div class="invoice-detail" *ngIf="!isLoading">
                        <div class="fare-head">Your Fare</div>
                        <div class="invoice-price"><i class="fa fa-rupee-sign"></i>{{ bookingData?.totalFare || (bookingRequest?.basicFare + bookingRequest?.gst) }}</div>
                        <div class="fare-breakdown">
                            <h2>Fare Breakdown</h2>
                            <div class="fares-row">
                                <span class="rows">Base fare</span>
                                <span class="fareprice"><i class="fa fa-rupee-sign"></i> {{ bookingData?.basicFare || bookingRequest?.basicFare }}</span>
                            </div>

                            <!--<div class="fares-row">
                                <span class="rows">Toll, Surcharges and fees <i class="fa fa-question-circle"></i></span>
                                <span class="fareprice"><i class="fa fa-rupee-sign"></i> 20.20</span>
                            </div>-->

                            <div class="fares-row">
                                <span class="rows">Taxes IGST(5%) </span>
                                <span class="fareprice"><i class="fa fa-rupee-sign"></i>{{ bookingData?.gst || bookingRequest?.gst }}</span>
                            </div>

                            <!-- Show Amount Paid for partial payments -->
                            <div class="fares-row" *ngIf="bookingData?.paymentType === 'PARTIAL'">
                                <span class="rows">Amount Paid Online</span>
                                <span class="fareprice"><i class="fa fa-rupee-sign"></i>{{ bookingData?.amountPaid || 0 }}</span>
                            </div>

                            <div class="fares-row">
                                <span class="rows">Amount to be paid to Driver</span>
                                <span class="fareprice"> <i class="fa fa-rupee-sign"></i>{{ bookingData?.remainingAmountForDriver || 0 }}</span>
                            </div>
                        </div>

                        <div class="payment-by">
                          <div class="payment-type"><i class="fa fa-money-check"></i> Payment</div>
                          <div class="paid">
                            <span class="fa fa-check"></span>
                            <span *ngIf="bookingData?.paymentType === 'FULL'">Paid Online</span>
                            <span *ngIf="bookingData?.paymentType === 'PARTIAL'">Paid Partially Online</span>
                            <span *ngIf="!bookingData?.paymentType">Paid by cash</span>
                          </div>
                        </div>

                        <div class="help">
                          <div>Need Help?</div>
                          <small>Tap help in your app to contact us with questions about your trip. For T&C and fare details, visit out website</small>
                        </div>

                        <!--<button type="button" class="download-btn">
                          Download Receipt
                        </button>-->

                    </div>

                </div>


                  
                
    </div>
    
    

  
    
  </section>
          
          <!-- Modal -->
<div class="modal fade" id="myModal" role="dialog">
<div class="modal-dialog">

<!-- Modal content-->
<div class="modal-content">
  <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal">&times;</button>
    <h4 class="modal-title">Modal Header</h4>
  </div>
  <div class="modal-body">
    <p>Some text in the modal.</p>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
  </div>
</div>

</div>
</div>
  

    
  
</div>








