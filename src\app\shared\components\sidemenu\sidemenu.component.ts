import { Component, OnInit } from '@angular/core';
import * as $ from 'jquery';
import { AuthService } from '../../services';
import { LoggerService } from '../../interceptors/logger.service';

@Component({
  selector: 'app-sidemenu',
  templateUrl: './sidemenu.component.html',
  styleUrls: ['./sidemenu.component.css']
})
export class SidemenuComponent implements OnInit {

  constructor(private authService: AuthService) { 
    this.logger = LoggerService.createLogger('SidemenuComponent');
  }

  private logger: LoggerService;

  isUserLoggedIn: boolean;

  ngOnInit(): void {
    this.initFields();
    
    const component = this;

    //Toggle Click Function
    $(".menu-toggle").click(function (e) {
      component.logger.trace('toggle trigger', e);
      e.preventDefault();
      $("#wrapper").toggleClass("toggled");
    });

    this.authService.user$.subscribe(
      (user) => {
        this.logger.debug('subscribed from auth service user', user);
        if (user) {
          this.isUserLoggedIn = true;
        } else {
          this.isUserLoggedIn = false;
        }
        this.logger.debug('app side menu isUserLoggedIn', this.isUserLoggedIn);
      }
    );
  }

  private initFields = () => {
    this.isUserLoggedIn = false;
  }

}
