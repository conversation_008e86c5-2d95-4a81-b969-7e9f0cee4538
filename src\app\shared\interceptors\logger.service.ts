enum LogLevel {
  Trace = 0,
  Debug = 1,
  Info = 2,
  Warn = 3,
  <PERSON>rror = 4,
  Fatal = 5,
  Off = 6
}

class LogConfig {
  static level: LogLevel = LogLevel.Off;
  // static level: LogLevel = LogLevel.Trace;
}

export class LoggerService {
  private class: string;

  static createLogger = (className: string) => {
    return new LoggerService(className);
  }

  private constructor(className: string) {
    this.class = className;
  }

  private log = (level: string, args) => {
    console.log(new Date().toLocaleString(), level, '[' + this.class + ']', ...args);
  }

  private logError = (args) => {
    console.error(new Date().toLocaleString(), '[ERROR]', '[' + this.class + ']', ...args);
  }

  trace = (...args) => {
    if (this.isTraceEnabled()) {
      this.log('[TRACE]', args);
    }
  }

  debug = (...args) => {
    if (this.isDebugEnabled()) {
      this.log('[DEBUG]', args);
    }
  }

  info = (...args) => {
    if (this.isInfoEnabled()) {
      this.log('[INFO]', args);
    }
  }

  error = (...args) => {
    if (this.isErrorEnabled()) {
      this.logError(args);
    }
  }

  isTraceEnabled = (): boolean => {
    return LogConfig.level <= LogLevel.Trace;
  }

  isDebugEnabled = (): boolean => {
    return LogConfig.level <= LogLevel.Debug;
  }

  isInfoEnabled = (): boolean => {
    return LogConfig.level <= LogLevel.Info;
  }

  isErrorEnabled = (): boolean => {
    return LogConfig.level <= LogLevel.Error;
  }
}
