import { Injectable } from '@angular/core';
import { MmiService } from '../../services/mmi/mmi-service.service';
import { HttpRequest, <PERSON>ttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class MMIErrorInterceptor implements HttpInterceptor  {

  constructor(private authenticationService: MmiService) { }

    intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        return next.handle(request).pipe(catchError(err => {
            if (err.status === 401)
             {
                // auto logout if 401 response returned from api
              //  this.authenticationService.logout();
               // location.reload(true);
            }

            const error = err.error.message || err.statusText;
            return throwError(error);
        }));
    }

}
