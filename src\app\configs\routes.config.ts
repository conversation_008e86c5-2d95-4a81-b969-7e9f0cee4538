import {InjectionToken} from '@angular/core';

export let  ROUTES_CONFIG = new InjectionToken('routes.config');

const basePaths = {
  cabyaari: 'cabyaari',
};

const routesNames = {
  home: '',
  error404: '404',
  cabyaari: {
    basePath: basePaths.cabyaari
  }
};

export const RoutesConfig: any = {
  routesNames,
  routes: {
    home: `/${routesNames.home}`,
    error404: `/${routesNames.error404}`,
    cabyaari: {
      detail: getCabYaariDetail
    }
  }
};

export function getCabYaariDetail(id) {
  return `/${basePaths.cabyaari}/${id}`;
}
